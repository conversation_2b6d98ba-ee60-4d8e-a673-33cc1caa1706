from proterra.txn import util as txn_util


def foil_inspection_result(base_tagpath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        read_error = False
        txn_data = {}
        # Script Specific Variables
        serial_number = ''
        status = True
        inspection_data = ''

        ###################################
        # DATA COLLECTION
        try:
            txn_inputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txn_data = txn_util.read_txn_data(txn_inputs, base_tagpath)
            # Looping over read values for null
            for data in txn_data:
                if txn_data[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "Barcode" in txn_data["PLCData"].keys():
                serial_number = txn_util.parse_mes_barcode(txn_data["PLCData"]["Barcode"])
                status = txn_data["PLCData"]["Status"]
                if status == False:
                    inspection_data = txn_data["PLCData"]["DataValues"]
                # Write values to UDT tags
                system.tag.writeBlocking([base_tagpath+"/SerialNumber"], [serial_number])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            read_error = True

        print txn_data
        read_error = False
        # SPROC PREP
        call = system.db.createSProcCall("[txn_sp].[OperatorStation_VisionSystem_FoilInspectionResult]", "REVMES")
        if not read_error:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txn_data['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txn_data['WorkOrderId'])
            call.registerInParam("p_AssetId", system.db.INTEGER, txn_data['AssetId'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txn_data['ProcessStep'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txn_data['UserName'])
            call.registerInParam("p_SerialNumber", system.db.NVARCHAR, serial_number)

        # Performing standard TXN Actions (Sproc, Response, Log)
        txn_util.execute_txn(base_tagpath, txn_start, read_error, txn_data, call)

    # For any other exception not handled
    except Exception as ex:
        txn_util.handle_uncaught_exceptions(ex, base_tagpath)
