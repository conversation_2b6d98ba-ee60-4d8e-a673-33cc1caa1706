/* Alarm Tables Toolbar */

.ia_alarmTableComponent__toolbar {
    background-color: var(--containerNested);
    border-bottom: var(--containerBorder);
}

.ia_alarmTableComponent__toolbar__tab {
    color: var(--neutral-50);
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.125rem;
    border-bottom: 3px solid transparent;
}

.ia_alarmTableComponent__toolbar__tab:hover:not(.ia_alarmTableComponent__toolbar__tab--active),
.ia_alarmTableComponent__toolbar__tab:hover:not(.ia_alarmTableComponent__toolbar__tab--active) .ia_alarmTableComponent__toolbar__tab__icon {
    color: var(--neutral-60);
}

.ia_alarmTableComponent__toolbar__tab--active {
    color: var(--neutral-90);
    font-weight: 500;
    border-bottom: 3px solid var(--neutral-50);
}

.ia_alarmTableComponent__toolbar__tab__icon {
    color: var(--neutral-50);
}

.ia_alarmTableComponent__toolbar__controlIcon,
.ia_alarmTableComponent__toolbar__tab__icon--active {
    color: var(--neutral-70);
}

.ia_alarmTableComponent__toolbar__controlIcon--active {
    color: var(--callToAction);
}

.ia_alarmTableComponent__toolbar__filter__preFilterButton {
    outline: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.ia_alarmTableComponent__toolbar__filter__preFilterButton--active {
    background-color: var(--callToActionHighlight);
}

.ia_alarmTableComponent__toolbar__activeFilterInput {
    background-color: var(--input);
    border-left: none;
    height: 2rem;
    outline: none;
}

.ia_alarmTableComponent__toolbar__filterResults {
    font-size: 0.75rem;
    background-color: var(--container);
    border-top: 1px solid var(--border);
}

.ia_alarmTableComponent__toolbar__preFilterCount {
    font-size: 0.625rem;
    color: var(--neutral-90);
    background-color: var(--containerNested);
}

.ia_alarmTableComponent__toolbar__preFilter__removeAll {
    border-left: var(--containerBorder);
    background-color: var(--containerNested);
}

.ia_alarmTableComponent__toolbar__filter__preFilterContainer {
    background-color: var(--container);
    border-top: var(--containerBorder);
}

.ia_alarmTableComponent__preFilterPill {
    border: var(--containerBorder);
    background-color: var(--neutral-20);
}

.ia_alarmTableComponent__preFilterPill__title {
    color: var(--neutral-90);
    font-size: 0.75rem;
    line-height: 0.875rem;
}

.ia_alarmTableComponent__preFilters__filter__pager {
    background-color: var(--neutral-50);
    color: var(--white);
    border-left: var(--containerBorder);
}

.ia_alarmTableComponent__preFilters__filter__pager:hover {
    background-color: var(--neutral-40);
}

.ia_alarmTableComponent__preFilters__filter__pager:active {
    background-color: var(--neutral-60);
}

.ia_alarmTableComponent__preFilters__filter__pager--disabled {
    background-color: var(--neutral-60);
    box-shadow: none;
}

.ia_alarmTableComponent__preFilters__filter__pager--left {
    box-shadow: 5px 0px 10px 0px rgba(0, 0, 0, 0.2);
    border-left: var(--containerBorder);
}

.ia_alarmTableComponent__preFilters__filter__pager--right {
    box-shadow: -5px 0px 10px 0px rgba(0, 0, 0, 0.2);
}

.ia_alarmTableComponent__filter__searchPlaceholder,
.ia_alarmTableComponent__filter__closeToggle {
    background-color: var(--input);
    border: var(--containerBorder);
}

/* Alarm Tables Modal Menu */

.ia_alarmTableComponent__modalMenuHeader {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--neutral-90);
    background-color: var(--containerNested);
}

.ia_alarmTableComponent__modalMenuTitle {
    font-size: 0.75rem;
    border-bottom: var(--containerBorder);
    color: var(--neutral-90);
}

.ia_alarmTableComponent__modalMenuItem {
    color: var(--neutral-90);
    line-height: 2rem;
    font-size: 0.875rem;
}

.ia_alarmTableComponent__modalMenuItem:hover {
    color: var(--neutral-90);
    background-color: var(--callToActionHighlight);
}

.ia_alarmTableComponent__modalMenuCategory {
    background-color: var(--container);
    font-weight: 500;
    font-size: 0.75rem;
    color: var(--neutral-60);
    border-top: var(--containerBorder);
    border-bottom: var(--containerBorder);
}

.ia_alarmTableComponent__body__row {
    color: var(--neutral-90);
    border-bottom: var(--containerBorder);
}

.ia_alarmTableComponent__foot {
    border: 0px solid var(--border);
    background-color: var(--containerNested);
    color: var(--neutral-90);
}

.ia_alarmTableComponent__foot--show {
    border: var(--containerBorder);
}

/* Alarm Table Alarm Details */

.ia_alarmTableComponent__alarmDetails__titleBar {
    background-color: var(--neutral-30);
    font-size: 0.875rem;
    line-height: 1rem;
    color: var(--neutral-90);
    box-shadow: 0px 13px 15px -1px rgba(0, 0, 0, 0.15);
    border-bottom: var(--containerBorder);
}

.ia_alarmTableComponent__alarmDetails__category {
    background-color: var(--neutral-10);
}

.ia_alarmTableComponent__alarmDetails__category__item {
    color: var(--neutral-90);
    font-size: 0.875rem;
    line-height: 1rem;
    border-top: var(--containerBorder);
}

.ia_alarmTableComponent__alarmDetails__category__item__label {
    border-right: var(--containerBorder);
}

.ia_alarmTableComponent__alarmDetails__category__titleBar {
    background-color: var(--neutral-10);
    border-top: var(--containerBorder);
    color: var(--neutral-90);
    font-weight: 600;
    font-size: 0.75rem;
    line-height: 0.875rem;
}

.ia_alarmTableComponent__alarmDetails__notesContainer {
    background-color: var(--neutral-10);
}

.ia_alarmTableComponent__alarmNotesModal__titleBar {
    background-color: var(--neutral-30);
    box-shadow: 0px 13px 15px -1px rgba(0, 0, 0, 0.15);
    font-size: 0.875rem;
    line-height: 1rem;
    color: var(--neutral-90);
}

.ia_alarmPropertiesTable {
    border-bottom: var(--containerBorder);
    background-color: var(--container);
}

.ia_alarmPropertiesTable__category__titleBar {
    border-top: var(--containerBorder);
    background-color: var(--neutral-10);
    color: var(--border);
    font-weight: 600;
    font-size: 0.75rem;
    line-height: 0.875em;
}

.ia_alarmPropertiesTable__categoryTable__row {
    color: var(--neutral-90);
    border-top: var(--containerBorder);
    font-size: 0.75rem;
    line-height: 0.875em;
}

.ia_alarmPropertiesTable__categoryTable__row__label {
    border-right: var(--containerBorder);
}
