.ia_accordionComponent {
    border: var(--containerBorder);
    background-color: var(--container);
}

.ia_accordionComponent--disabled {
    color: var(--label--disabled);
    background-color: var(--input--disabled);
    cursor: not-allowed;
}

.ia_accordionComponent__body {
    border-bottom: var(--containerBorder);
}

.ia_accordionComponent__header {
    background-color: var(--container);
    cursor: pointer;
}

.ia_accordionComponent__header:hover {
    filter: brightness(0.9);
}

.ia_accordionComponent__header__text {
    font-size: 14px;
}

.ia_accordionComponent__header__text--expanded {
    font-weight: 500;
  }

.ia_accordionComponent__emptyRow {
    background-color: var(--containerNested);
}