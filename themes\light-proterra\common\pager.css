.ia_pager {
    background-color: var(--neutral-30);
    color: var(--neutral-90);
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);
    line-height: 0.875rem;
    font-size: 0.75rem;
}

.ia_pager__jumpFirstLast {
    line-height: 0.875rem;
    font-size: 1rem;
    color: var(--neutral-90);
}

.ia_pager__jumpFirstLast--disabled {
    color: var(--label--disabled);
}

.ia_pager__prevNext {
    color: var(--neutral-90);
}

.ia_pager__prevNext--disabled {
    color: var(--neutral-70);
}

.ia_pager__page {
    border-radius: 50%;
    width: 1.25rem;
    height: 1.25rem;
    color: var(--neutral-90);
}

.ia_pager__page--active {
    background: var(--callToAction);
    color: var(--neutral-10);
}

.ia_pager__jump {
    width: 3.125rem;
    text-align: center;
    border-radius: var(--borderRadius);
}

.ia_pager__jump:focus {
    outline: none;
}
