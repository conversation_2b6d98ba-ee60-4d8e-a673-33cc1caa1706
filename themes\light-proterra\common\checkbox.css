.ia_checkbox__checkedIcon,
.ia_checkbox__uncheckedIcon,
.ia_checkbox__indeterminateIcon {
    transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.ia_checkbox__icon--useColor {
    fill: currentColor;
}

.ia_checkbox .ia_checkbox__checkedIcon,
.ia_checkbox .ia_checkbox__indeterminateIcon {
    color: var(--checkbox--checked);
}

.ia_checkbox .ia_checkbox__uncheckedIcon {
    color: var(--checkbox--unchecked);
}

/* Disabled checkbox style */
.ia_checkbox .ia_checkbox__checkedIcon--disabled,
.ia_checkbox .ia_checkbox__uncheckedIcon--disabled,
.ia_checkbox .ia_checkbox__indeterminateIcon--disabled {
    color: var(--checkbox--disabled);
}

/* Add hover styles for enabled and checked */
.ia_checkbox:hover input:enabled~.ia_checkbox__checkedIcon,
.ia_checkbox:hover input:enabled~.ia_checkbox__indeterminateIcon {
    filter: brightness(1.2);
}

/* Add hover styles for enabled and unchecked */
.ia_checkbox:hover input:enabled~.ia_checkbox__uncheckedIcon {
    filter: brightness(0.5);
}

.ia_checkbox input:enabled:active~.ia_checkbox__checkedIcon,
.ia_checkbox input:enabled:active~.ia_checkbox__uncheckedIcon,
.ia_checkbox input:enabled:active~.ia_checkbox__indeterminateIcon {
    filter: brightness(0.7);
}
