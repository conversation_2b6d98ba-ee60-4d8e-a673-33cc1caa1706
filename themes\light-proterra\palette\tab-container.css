.ia_tabContainerComponent__content {
    border-left: var(--containerBorder);
    border-right: var(--containerBorder);
    border-bottom: var(--containerBorder);
}

.ia_tabContainerComponent__tab--classic {
    color: var(--neutral-60);
    border-top: var(--containerBorder);
    border-right: var(--containerBorder);
    border-bottom: var(--containerBorder);
    font-size: 0.875rem;
}

.ia_tabContainerComponent__tabMenuRight--classic {
    border-bottom: var(--containerBorder);
}

.ia_tabContainerComponent__tab--classic:first-child {
    border-left: var(--containerBorder);
}

.ia_tabContainerComponent__tab--classic:not(.ia_tabContainerComponent__tab--classic--active,
.ia_tabContainerComponent__tab--classic--disabled):hover {
    filter: brightness(0.9);
}

.ia_tabContainerComponent__tab--classic--active {
    background-color: var(--container);
    font-weight: 500;
    color: var(--neutral-90);
    border-top: var(--containerBorder);
    border-bottom: none;
}

.ia_tabContainerComponent__tab--classic--disabled {
    background-color: var(--neutral-40);
    color: var(--neutral-60);
    opacity: 0.6
}

.ia_tabContainerComponent__tab--modern {
    color: var(--neutral-70);
    border-bottom: var(--containerBorder);
    box-shadow: none;
    transition: box-shadow 0.2s linear;
    font-size: 0.875rem;
}

.ia_tabContainerComponent__tabMenuRight--modern {
    border-bottom: var(--containerBorder);
}

.ia_tabContainerComponent__tab--modern--active {
    font-weight: 500;
    color: var(--neutral-90);

    /* Provides the effect of a bottom border without increasing the height of block */
    box-shadow: inset 0 -3px var(--border);
}

.ia_tabContainerComponent__tab--modern--disabled {
    color: var(--neutral-60);
    opacity: 0.6
}

.ia_tabContainerComponent__tab--modern:not(.ia_tabContainerComponent__tab--modern--active,
.ia_tabContainerComponent__tab--modern--disabled):hover {
    filter: brightness(0.8);
}

.ia_tabContainerComponent--designing__deleteConfirmation {
    color: var(--label);
    background-color: var(--containerNested);
    border: var(--containerBorder);
}

.ia_tabContainerComponent--designing__deleteConfirmation__delete {
    font-size: 0.75rem;
    background-color: var(--error);
}

.ia_tabContainerComponent--designing__deleteConfirmation__cancel {
    font-size: 0.75rem;
}

.ia_tabContainerComponent--designing__tab--pendingDelete {
    border: var(--containerBorder);
    border-color: var(--error);
}
