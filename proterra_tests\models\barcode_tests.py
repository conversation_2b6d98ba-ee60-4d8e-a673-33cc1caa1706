import platform
from proterra.models import barcode
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner
from mock import patch, MagicMock


@patch('system.db.runScalarPrepQuery')
@patch('system.db.runPrepQuery')
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class GetSerialTests(TestCase):
    def test1(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('000017425942312304521301002375111E01')
        self.assertEqual('000017425942312304521301002375111E01', serial)

    def test2(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('000019839722426001605201002375111E03')
        self.assertEqual('000019839722426001605201002375111E03', serial)

    def test3(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('1599052550C0212312023K23L0640001300149347')
        self.assertEqual('1599052550C0212312023K23L0640001300149347', serial)

    def test4(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01')
        self.assertEqual('1599194-2464D01PAPPEGF10202410281300001108101', serial)

    def test5(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551')
        self.assertEqual('K024A210010551', serial)

    def test6(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('3661 | 133-0468 | C.1 | 102723 | K023A23001   1432')
        self.assertEqual('K023A230011432', serial)

    def test7(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('3661 | 133-0468 | C.1 | 102723 | K023A23001 \t 1432')
        self.assertEqual('K023A230011432', serial)

    def test8(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('4222187-5071A.1061020230285')
        self.assertEqual('4222187-5071A.1061020230285', serial)

    def test9(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('4222195-3560A.1_AZF030120240743')
        self.assertEqual('4222195-3560A.1AZF030120240743', serial)

    def test10(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('4222 | 195-3560 | A.1 | JT | 08302024 | 0022')
        self.assertEqual('4222195-3560A.1JT083020240022', serial)

    def test11(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('199-4631 | A01 | GVB | M2432720000189')
        self.assertEqual('M2432720000189', serial)

    def test12(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('199-4631 | A01 | GVB | M2432720000190')
        self.assertEqual('M2432720000190', serial)

    def test13(self, runPrepQuery, runScalarPrepQuery):
        runPrepQuery.return_value = []
        serial = barcode.get_serial('M2432720000189')
        self.assertEqual('M2432720000189', serial)
        test_dataset = [{
            'ItemSerialNoId': 1,
            'SerialNoNumber': 'M2432720000189',
            'RawBarcode': '199-4631 | A01 | GVB | M2432720000189'
        }]
        runPrepQuery.return_value = test_dataset
        serial = barcode.get_serial('M2432720000189')
        self.assertEqual('M2432720000189', serial)

    def test14(self, runPrepQuery, runScalarPrepQuery):
        runPrepQuery.return_value = []
        serial = barcode.get_serial('M2432720000190')
        self.assertEqual('M2432720000190', serial)
        test_dataset = [{
            'ItemSerialNoId': 1,
            'SerialNoNumber': 'M2432720000190',
            'RawBarcode': '199-4631 | A01 | GVB | M2432720000190'
        }]
        runPrepQuery.return_value = test_dataset
        serial = barcode.get_serial('M2432720000190')
        self.assertEqual('M2432720000190', serial)

    def test15(self, runPrepQuery, runScalarPrepQuery):
        runPrepQuery.return_value = []
        runScalarPrepQuery.return_value = None
        serial = barcode.get_serial('K024A21001 | 0551')
        self.assertEqual('K024A210010551', serial)
        test_dataset = [{
            'ItemSerialNoId': 1,
            'SerialNoNumber': 'K024A210010551',
            'RawBarcode': '3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551'
        }]
        runPrepQuery.return_value = test_dataset
        serial = barcode.get_serial('K024A21001 | 0551')
        self.assertEqual('K024A210010551', serial)

    def test16(self, runPrepQuery, runScalarPrepQuery):
        runPrepQuery.return_value = []
        serial = barcode.get_serial('K023A23001   1432')
        self.assertEqual('K023A230011432', serial)
        test_dataset = [{
            'ItemSerialNoId': 1,
            'SerialNoNumber': 'K023A230011432',
            'RawBarcode': '3661 | 133-0447 | C.1 | 241028 | K023A23001   1432'
        }]
        runPrepQuery.return_value = test_dataset
        serial = barcode.get_serial('K023A23001   1432')
        self.assertEqual('K023A230011432', serial)

    def test17(self, runPrepQuery, runScalarPrepQuery):
        runPrepQuery.return_value = []
        serial = barcode.get_serial('K023A23001 \t 1432')
        self.assertEqual('K023A230011432', serial)
        test_dataset = [{
            'ItemSerialNoId': 1,
            'SerialNoNumber': 'K023A230011432',
            'RawBarcode': '3661 | 133-0447 | C.1 | 241028 | K023A23001 \t 1432'
        }]
        runPrepQuery.return_value = test_dataset
        serial = barcode.get_serial('K023A23001 \t 1432')
        self.assertEqual('K023A230011432', serial)

    def test18(self, runPrepQuery, runScalarPrepQuery):
        runPrepQuery.return_value = []
        runScalarPrepQuery.return_value = None
        serial = barcode.get_serial('K0 | 24A2 | 1001 | 0551')
        self.assertEqual('K0|24A2|1001|0551', serial)
        test_dataset = []
        runPrepQuery.return_value = test_dataset
        serial = barcode.get_serial('K0 | 24A2 | 1001 | 0551')
        self.assertEqual('K0|24A2|1001|0551', serial)

    def test19(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_serial('3661|133-0468|C.1|102723|K023A23001 0013')
        self.assertEqual('K023A230010013', serial)

    def test20(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_pallet_serial('146-3578 | A.1 | 1166')
        self.assertEqual('1166', serial)

    def test21(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_pallet_serial('1166')
        self.assertEqual('1166', serial)

    def test22(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_pallet_serial('146-3578 | A.1 | 1166\u0000')
        self.assertEqual('1166', serial)

    def test23(self, runPrepQuery, runScalarPrepQuery):
        serial = barcode.get_pallet_serial('1166\u0000\u0000')
        self.assertEqual('1166', serial)


@patch('system.db.runScalarPrepQuery', MagicMock())
@patch('system.db.runPrepQuery', MagicMock())
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class GetPartNumberTests(TestCase):
    def test1(self):
        part_no = barcode.get_part_number('000017425942312304521301002375111E01')
        self.assertEqual('174-2594', part_no)

    def test2(self):
        part_no = barcode.get_part_number('000019839722426001605201002375111E03')
        self.assertEqual('198-3972', part_no)

    def test3(self):
        part_no = barcode.get_part_number('1599052550C0212312023K23L0640001300149347')
        self.assertEqual('052550', part_no)

    def test4(self):
        part_no = barcode.get_part_number('1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01')
        self.assertEqual('194-2464', part_no)

    def test5(self):
        part_no = barcode.get_part_number('3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551')
        self.assertEqual('133-0447', part_no)

    def test6(self):
        part_no = barcode.get_part_number('3661 | 133-0468 | C.1 | 102723 | K023A23001   1432')
        self.assertEqual('133-0468', part_no)

    def test7(self):
        part_no = barcode.get_part_number('3661 | 133-0468 | C.1 | 102723 | K023A23001 \t 1432')
        self.assertEqual('133-0468', part_no)

    def test8(self):
        part_no = barcode.get_part_number('4222187-5071A.1061020230285')
        self.assertEqual('187-5071', part_no)

    def test9(self):
        part_no = barcode.get_part_number('4222195-3560A.1_AZF030120240743')
        self.assertEqual('195-3560', part_no)

    def test10(self):
        part_no = barcode.get_part_number('4222 | 195-3560 | A.1 | JT | 08302024 | 0022')
        self.assertEqual('195-3560', part_no)

    def test11(self):
        part_no = barcode.get_part_number('199-4631 | A01 | GVB | M2432720000189')
        self.assertEqual('199-4631', part_no)

    def test12(self):
        part_no = barcode.get_part_number('199-4631 | A01 | GVB | M2432720000190')
        self.assertEqual('199-4631', part_no)

    def test13(self):
        part_no = barcode.get_part_number('3661|133-0468|C.1|102723|K023A23001 0013')
        self.assertEqual('133-0468', part_no)

    def test14(self):
        part_no = barcode.get_pallet_part_number('146-3578 | A.1 | 1166')
        self.assertEqual('146-3578', part_no)

    def test15(self):
        part_no = barcode.get_pallet_part_number('1166')
        self.assertEqual('', part_no)

    def test16(self):
        part_no = barcode.get_pallet_part_number('146-3578 | A.1 | 1166\u0000')
        self.assertEqual('146-3578', part_no)

    def test17(self):
        part_no = barcode.get_pallet_part_number('1166\u0000\u0000')
        self.assertEqual('', part_no)


@patch('system.db.runScalarPrepQuery', MagicMock())
@patch('system.db.runPrepQuery', MagicMock())
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class GetRevTests(TestCase):
    def test1(self):
        rev = barcode.get_rev('000017425942312304521301002375111E01')
        self.assertEqual('', rev)

    def test2(self):
        rev = barcode.get_rev('000019839722426001605201002375111E03')
        self.assertEqual('', rev)

    def test3(self):
        rev = barcode.get_rev('1599052550C0212312023K23L0640001300149347')
        self.assertEqual('C02', rev)

    def test4(self):
        rev = barcode.get_rev('1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01')
        self.assertEqual('D01', rev)

    def test5(self):
        rev = barcode.get_rev('3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551')
        self.assertEqual('C.1', rev)

    def test6(self):
        rev = barcode.get_rev('3661 | 133-0468 | C.1 | 102723 | K023A23001   1432')
        self.assertEqual('C.1', rev)

    def test7(self):
        rev = barcode.get_rev('3661 | 133-0468 | C.1 | 102723 | K023A23001 \t 1432')
        self.assertEqual('C.1', rev)

    def test8(self):
        rev = barcode.get_rev('4222187-5071A.1061020230285')
        self.assertEqual('A.1', rev)

    def test9(self):
        rev = barcode.get_rev('4222195-3560A.1_AZF030120240743')
        self.assertEqual('A.1', rev)

    def test10(self):
        rev = barcode.get_rev('4222 | 195-3560 | A.1 | JT | 08302024 | 0022')
        self.assertEqual('A.1', rev)

    def test11(self):
        rev = barcode.get_rev('199-4631 | A01 | GVB | M2432720000189')
        self.assertEqual('A01', rev)

    def test12(self):
        rev = barcode.get_rev('199-4631 | A01 | GVB | M2432720000190')
        self.assertEqual('A01', rev)

    def test13(self):
        rev = barcode.get_rev('3661|133-0468|C.1|102723|K023A23001 0013')
        self.assertEqual('C.1', rev)

    def test14(self):
        rev = barcode.get_pallet_rev('146-3578 | A.1 | 1166')
        self.assertEqual('A.1', rev)

    def test15(self):
        rev = barcode.get_pallet_rev('1166')
        self.assertEqual('', rev)

    def test16(self):
        rev = barcode.get_pallet_rev('146-3578 | A.1 | 1166\u0000')
        self.assertEqual('A.1', rev)

    def test17(self):
        rev = barcode.get_pallet_rev('1166\u0000\u0000')
        self.assertEqual('', rev)


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class BMBBarcodeTests(TestCase):
    def test1(self):
        serial = barcode.get_bmb_serial('000017425942312304521301002375111E01')
        self.assertEqual('000017425942312304521301002375111E01', serial)

    def test2(self):
        serial = barcode.get_bmb_serial('000019839722426001605201002375111E03')
        self.assertEqual('000019839722426001605201002375111E03', serial)


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class CassetteBarcodeTests(TestCase):
    def test1(self):
        serial = barcode.get_cassette_serial('1599052550C0212312023K23L0640001300149347')
        self.assertEqual('1599052550C0212312023K23L0640001300149347', serial)

    def test2(self):
        serial = barcode.get_cassette_serial('1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01')
        self.assertEqual('1599194-2464D01PAPPEGF10202410281300001108101', serial)


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class FoilBarcodeTests(TestCase):
    def test1(self):
        serial = barcode.get_foil_serial('3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551')
        self.assertEqual('K024A210010551', serial)

    def test2(self):
        serial = barcode.get_foil_serial('3661 | 133-0468 | C.1 | 102723 | K023A23001   1432')
        self.assertEqual('K023A230011432', serial)

    def test3(self):
        serial = barcode.get_foil_serial('3661 | 133-0468 | C.1 | 102723 | K023A23001 \t 1432')
        self.assertEqual('K023A230011432', serial)

    def test4(self):
        serial = barcode.get_foil_serial('3661|133-0468|C.1|102723|K023A23001 0013')
        self.assertEqual('K023A230010013', serial)


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class SpineBarcodeTests(TestCase):
    def test1(self):
        serial = barcode.get_spine_serial('4222187-5071A.1061020230285')
        self.assertEqual('4222187-5071A.1061020230285', serial)

    def test2(self):
        serial = barcode.get_spine_serial('4222195-3560A.1_AZF030120240743')
        self.assertEqual('4222195-3560A.1AZF030120240743', serial)

    def test3(self):
        serial = barcode.get_spine_serial('4222 | 195-3560 | A.1 | JT | 08302024 | 0022')
        self.assertEqual('4222195-3560A.1JT083020240022', serial)


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class PalletBarcodeTests(TestCase):
    def test1(self):
        serial = barcode.get_pallet_serial('146-3578 | A.1 | 1166')
        self.assertEqual('1166', serial)

    def test2(self):
        serial = barcode.get_pallet_serial('1166')
        self.assertEqual('1166', serial)

    def test3(self):
        serial = barcode.get_pallet_serial('146-3578 | A.1 | 1166\u0000')
        self.assertEqual('1166', serial)

    def test4(self):
        serial = barcode.get_pallet_serial('1166\u0000\u0000')
        self.assertEqual('1166', serial)


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class ProterraBarcodeTests(TestCase):
    def test1(self):
        serial = barcode.get_serial('199-4631 | A01 | GVB | M2432720000189')
        self.assertEqual('M2432720000189', serial)

    def test2(self):
        serial = barcode.get_serial('199-4631 | A01 | GVB | M2432720000190')
        self.assertEqual('M2432720000190', serial)

    def test3(self):
        serial = barcode.get_serial('199-4631 | A01 | GVB | M2432720000189\u0000')
        self.assertEqual('M2432720000189', serial)

    def test4(self):
        serial = barcode.get_serial('199-4631 | A01 | GVB | M2432720000190\u0000\u0000')
        self.assertEqual('M2432720000190', serial)


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            GetSerialTests,
            GetPartNumberTests,
            GetRevTests,
            BMBBarcodeTests,
            CassetteBarcodeTests,
            FoilBarcodeTests,
            SpineBarcodeTests,
            PalletBarcodeTests,
            ProterraBarcodeTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
