from java.lang import Exception as JavaException
from proterra.mes_transactions import txn, util
import json
import system.date
import system.util


############################################################################################################################################################
# Script: processAndInspection
# Description: 	This script receives a standard set of parameters that have been defined for communication with the control system for MAD to process test results
#
# Rev				Author			Date			Desc
# 0.1				JLA				01/19/2024		Initial Release
############################################################################################################################################################
def processAndInspection(baseTagPath):
    try:
        fnName = 'REVMES.MAD.Press.processAndInspection'
        fnlogger = system.util.getLogger(fnName)  # Enable for logging and/or quick troubleshooting...
        _fnstart = system.date.toMillis(system.date.now())

        # Values are initialized for the script
        readError = False
        success = False
        code = 0
        message = ''
        spineBarcode = ''
        blockBarcode = ''
        results = ''
        status = False
        # Values for the Press Process
        consumptionEventName = ''
        transferReasonId = 0
        operStepName = ''
        itemEventName = ''

        ###################################
        ######### DATA COLLECTION #########
        try:
            # Formatting the data payload
            plcData = system.tag.readBlocking([baseTagPath+"/Inputs/PathDATA"])[0].value
            dataBrowse = system.tag.browse(plcData)
            resultDict = {}
            payloadDict = {}
            # Looping through all the data from the PLC
            # for tag in dataBrowse:
            #	tagValue = tag['value'].value
            #	tagName = str(tag['name'])
            #	resultDict[tagName] = tagValue
            #	pass
            for tag in dataBrowse:
                tagValue = tag['value'].value
                tagName = str(tag['name'])
                if tag["hasChildren"]:
                    childrenDict = {}
                    tagName = str(tag['name'])
                    childrenDataBrowse = system.tag.browse(tag["fullPath"])
                    for child in childrenDataBrowse:
                        childrenDict[str(child['name'])] = child['value'].value
                    tagValue = childrenDict

                resultDict[tagName] = tagValue

            # Taking the values that will be used during the transaction
            if (resultDict["ImpedanceTestOK"] == True) and (resultDict["NutInstallOK"] == True):
                status = True
            spineBarcode = resultDict["SpineBarCode"]
            blockABarcode = resultDict["BlockABarCode"]
            blockBBarcode = resultDict["BlockBBarCode"]

            # Create the JSON payload with the result data
            payloadDict["Status"] = status
            payloadDict["SpineBarcode"] = spineBarcode
            payloadDict["BlockABarcode"] = blockABarcode
            payloadDict["BlockBBarcode"] = blockBBarcode
            payloadDict["Results"] = resultDict
            jsonDataDict = system.util.jsonDecode(str(json.dumps(resultDict)))
            jsonResults = str(json.dumps(jsonDataDict))

            # Reading the parameters and inputs
            # This is a tuple, in order to maintain the correct order
            txnInputs = [
                ('ProcessStep', "/Parameters.ProcessStep"),
                ('SendResponse', '/Parameters.SendResponse'),
                ('OperationStepName', '/Parameters.OperationStepName'),

                ('PathRSD', '/Inputs/PathRSD'),
                ('PathRESP', '/Inputs/PathRESP'),
                ('PathALARM', '/Inputs/PathALARM'),
                ('PathINPROCESS', '/Inputs/PathINPROCESS'),

                ('WorkOrderId', '/Inputs/WorkOrderId'),
                ('ReasonId', '/Inputs/ReasonId'),
                ('MaterialType', '/Inputs/MaterialType'),
                ('UserName', '/Inputs/UserName'),
                ('LocationId', '/Inputs/LocationId'),
                ('TestName', '/Inputs/TestName'),
                ('ItemVersionId', '/Inputs/ItemVersionId')
            ]

            # Looping to get the tag paths and read tags
            paths = []
            for inpt in txnInputs:
                paths.append(baseTagPath + inpt[1])
            tagsRead = system.tag.readBlocking(paths)

            # Looping to create the dictionary with the read values
            txnInputVal = {}
            inputCount = len(txnInputs)
            n = 0
            while (n < inputCount):
                txnInputVal[txnInputs[n][0]] = tagsRead[n].value
                n = n + 1

            # Taking more values that will be used during the transaction
            materialType = txnInputVal['MaterialType']
            sendResponse = txnInputVal['SendResponse']
            pathRESP = txnInputVal['PathRESP']
            pathRSD = txnInputVal['PathRSD']
            itemVersionId = txnInputVal['ItemVersionId']

            # Depending on the ProcessStep, sets up the values for the Press Process
            blockBarcode = ''
            if txnInputVal['ProcessStep'] == '17080':
                consumptionEventName = 'Half Module Press'
                transferReasonId = 115
                operStepName = 'PRESS HALF MODULE'
                itemEventName = 'Press: Block A + Spine Process'
                blockBarcode = blockABarcode

            if txnInputVal['ProcessStep'] == '17220':
                consumptionEventName = 'Full Module Press'
                transferReasonId = 132
                operStepName = 'PRESS FULL MODULE'
                itemEventName = 'Press: Block A+B+S Process'
                blockBarcode = blockBBarcode

            # DEBUG MAKE SURE THIS IS COMMENTED OUT !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            #spineBarcode = '174-8616|B01|GVB|1000349249'
            #blockBarcode = '133-0441|B04|GVB|4001235120'
            # DEBUG END

            serialNumberSpine, nonSerialInfoSpine, partNumberSpine, revisionSpine, partLocationSpine = Logic.Parse.FullSerialNumber(
                spineBarcode)
            serialNumberBlock, nonSerialInfoBlock, partNumberBlock, revisionBlock, partLocationBlock = Logic.Parse.FullSerialNumber(
                blockBarcode)

        except Exception as e1:
            readError = True
            code = 1015
            message = 'MES did not receive values in the expected DATA tag fields|%s' % (code)

        #########################
        ######### SPROC #########
        if not readError:
            sproc = """
			    EXECUTE [REVMES].mes.[sp_INSERT_MAD_PressProcessAndInspectionResult]
					@ItemVersionId = ?,
					@WorkOrderID = ?,
					@SpineSerialNumber = ?,
					@BlockSerialNumber = ?,
					@MaterialType = ?,
					@ToLocationId = ?,
					@TransferReasonId = ?,
					@TestReasonId = ?,
					@Username = ?,
					@TestOperationStepName = ?,
					@ConsumptionEventName = ?,
					@Status = ?,
					@ResultData = ?,
					@TestName = ?
			"""

            params = [
                itemVersionId,
                txnInputVal['WorkOrderId'],
                serialNumberSpine,
                serialNumberBlock,
                materialType,
                txnInputVal['LocationId'],
                transferReasonId,
                txnInputVal['ReasonId'],
                txnInputVal['UserName'],
                txnInputVal['OperationStepName'],
                consumptionEventName,
                status,
                jsonResults,
                txnInputVal['TestName']
            ]
            try:
                _dbstart = system.date.toMillis(system.date.now())
                sprocResult = system.dataset.toPyDataSet(system.db.runPrepQuery(sproc, params, 'MESDB'))
                _dbtime = system.date.toMillis(system.date.now()) - _dbstart
            except JavaException as e:
                raise Exception(repr(e))

            success = sprocResult[0][0] == 'Success'
            code = sprocResult[0][1]
            message = sprocResult[0][2]

            _fntime = system.date.toMillis(system.date.now()) - _fnstart
            msg = "{} -- Total: {} ms  -- DB: {} ms".format(fnName, _fntime, _dbtime)

        ############################
        ######### RESPONSE #########

        if sendResponse:
            ### Type (MAD)
            blockTypePath = pathRESP+'/Type'
            blockTypeValue = shared.PLCReturnsLookup.BlockTypeLookup_v3(itemVersionId)

            ### Series (MAD)
            moduleSeriesPath = pathRESP+'/Series'
            moduleSeriesValue = shared.PLCReturnsLookup.BlockSeriesLookup(itemVersionId)

            # Status
            statusPath = pathRESP+'/Status'
            statusValue = 5
            errorFlag = False

            if success:
                statusValue = 3
            else:
                parsedMessage = ''
                if message is not None:
                    parsedMessage = message.split("|")
                    if len(parsedMessage) > 1:
                        statusValue = int(parsedMessage[1])
                        code = statusValue
                        success = False
            # Write the response to the tags
            system.tag.writeBlocking([blockTypePath, moduleSeriesPath,  statusPath], [
                                     blockTypeValue, moduleSeriesValue,  statusValue])

        #######################
        ######### RSD #########
        txn.determineRSD(success, message, code, pathRSD, baseTagPath)

    # For any other exception not handled correctly
    except Exception as e:
        # Write trigger back low, log error - PCM
        print(repr(e))
        DataAccess.Log.LogError(repr(e), functionName=baseTagPath, application='transactionTriggerLogic', user='MES')
        system.tag.writeBlocking([
            baseTagPath+"/Status/Complete",
            baseTagPath+"/Status/Failed",
            baseTagPath+"/Status/FailureMessage",
            baseTagPath+"/Status/FailureCode",
            baseTagPath+"/Status/InProcess",
            baseTagPath+'/Status/Trigger'],

            [0, 1, repr(e),  9999, False, False])


def processAndInspectionResult(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        spineSerialNumber = ''
        processStatus = False
        testResults = ''
        blockSerialNumber = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/PathDATA', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "SpineBarCode" in txnData["PLCData"].keys():
                spineSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["SpineBarCode"])
                if txnData["ProcessStep"] == '17080':
                    blockSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["BlockABarCode"])
                if txnData["ProcessStep"] == '17220':
                    blockSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["BlockBBarCode"])

                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/SpineSerialNumber", baseTagPath +
                                         "/BlockSerialNumber"], [spineSerialNumber, blockSerialNumber])

                # Press Inspection Data
                dataBrowse = system.tag.browse(txnData["PathDATA"])
                resultDict = {}
                for tag in dataBrowse:
                    tagValue = tag['value'].value
                    tagName = str(tag['name'])
                    if tag["hasChildren"]:
                        childrenDict = {}
                        tagName = str(tag['name'])
                        childrenDataBrowse = system.tag.browse(tag["fullPath"])
                        for child in childrenDataBrowse:
                            childrenDict[str(child['name'])] = child['value'].value
                        tagValue = childrenDict
                    resultDict[tagName] = tagValue

                if (resultDict["ImpedanceTestOK"] == True) and (resultDict["NutInstallOK"] == True):
                    processStatus = True

                jsonDataDict = system.util.jsonDecode(str(json.dumps(resultDict)))
                testResults = str(json.dumps(jsonDataDict))
            else:
                raise Exception("Values missing in PLCData")
        except:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_MAD_Press_ProcessAndInspectionResult]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SpineSerialNumber", system.db.NVARCHAR, spineSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_TestResults", system.db.NVARCHAR, testResults)
            call.registerInParam("p_ProcessStatus", system.db.BIT, processStatus)
            call.registerInParam("p_BlockSerialNumber", system.db.NVARCHAR, blockSerialNumber)
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)
