.ia_piping {
    fill: var(--pipePrimaryFill);
    stroke: var(--pipeStroke);
}

.pipe_point__handle--selected {
    border-color: var(--callToAction);
}

.ia_pipe--pid {
    fill: var(--pipeStroke);
}

.ia_pipeSegment {
    pointer-events: fill;
}

.ia_pipeSegment--selected {
    stroke: var(--pipeSelectStroke);
}

.ia_pipeSegmentNode--pid {
    stroke: none;
}

.ia_pipeSegmentNode--pid--selected {
    fill: var(--pipeSelectStroke);
}

.ia_pipeSegmentOverlay {
    stroke: none;
}

.ia_pipeSegmentNode--selected {
    stroke: var(--pipeSelectStroke);
    stroke-dasharray: 5, 1;
}

.ia_pipeSegmentPrimaryStopColor {
    stop-color: var(--pipePrimaryFill);
}

.ia_pipeSegmentSecondaryStopColor {
    stop-color: var(--pipeSecondaryFill);
}
