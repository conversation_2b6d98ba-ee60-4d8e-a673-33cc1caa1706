from java.lang import Exception as JavaException
import system.db
from system.db import INTEGER, NVARCHAR
import system.util
import traceback

_logger = system.util.getLogger('proterra.transactions.serial_number')


class SerialNumber:
    _id = -1
    _item_version_id = -1
    _serial_number = ''

    def __init__(self, serial_number):
        # Define the query
        query = '''
            SELECT
                [ItemSerialNoId],
                [ItemVersionId],
                [SerialNoNumber]
            FROM [mes].[Item_SerialNo]
            WHERE SerialNoNumber=?
        '''
        # Define the arguments
        args = [serial_number]
        try:
            # Run the query
            results = system.db.runPrepQuery(query, args, 'MESDB')
            if results:
                self._id = int(results[0].get('ItemSerialNoId', -1))
                self._item_version_id = int(results[0].get('ItemVersionId', -1))
                self._serial_number = str(results[0].get('SerialNoNumber', ''))
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())

    @property
    def id(self):
        return self._id

    @property
    def item_version_id(self):
        return self._item_version_id

    @property
    def serial_number(self):
        return self._serial_number

    @staticmethod
    def create(item_version_id, serial_number, asset_id, user):
        """
        Create a new serial number in the database

        :param item_version_id: The item version ID associated with the serial number
        :type item_version_id: int
        :param barcode: The full barcode for the serial number
        :type barcode: str
        """
        # Define the query
        query = '''
            INSERT INTO [mes].[Item_SerialNo] (
                [ItemVersionId],
                [SerialNoNumber],
                [ItemSerialNoStateId],
                [AssetId],
                [CreatedBy],
                [EditedBy]
            )
            VALUES (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
            )
        '''
        # Define the arguments
        args = [
            item_version_id,
            serial_number,
            1,
            asset_id,
            user,
            user
        ]
        try:
            # Run the query
            system.db.runPrepUpdate(query, args, 'MESDB')
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())
        return SerialNumber(serial_number)

    @staticmethod
    def produce(item_version_id, asset_id, prefix, line, work_order_id, user):
        """
        Produce a new serial number in the database

        :param item_version_id: The item version ID associated with the serial number
        :type item_version_id: int
        :param asset_id: The asset ID associated with the serial number
        :type asset_id: int
        :param prefix: The prefix for the serial number
        :type prefix: str
        :param line: The line number for the serial number
        :type line: int
        :param work_order_id: The work order ID associated with the serial number
        :type work_order_id: int
        :param user: The user performing the generation
        :type user: str
        """
        serial_number = ''
        try:
            # Create the sproc call
            sproc = system.db.createSProcCall('[mes].[sp_GENERATE_ItemSerialNo]', 'MESDB')
            # Define the sproc parameters
            sproc.registerInParam('ItemVersionId', INTEGER, item_version_id)
            sproc.registerInParam('AssetId', INTEGER, asset_id)
            sproc.registerInParam('PartTypeLetter', NVARCHAR, prefix)
            sproc.registerInParam('LineNumber', INTEGER, line)
            sproc.registerInParam('WorkOrderId', INTEGER, work_order_id)
            sproc.registerInParam('Username', NVARCHAR, user)
            sproc.registerOutParam('SerialNumberGenerated', NVARCHAR)
            # Execute the sproc
            system.db.execSProcCall(sproc)
            # Get the generated serial number
            serial_number = str(sproc.getOutParamValue('SerialNumberGenerated'))
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())
        return SerialNumber(serial_number)

    def consume(self, work_order_id, from_serial_number, asset_id, user):
        """
        Consume a serial number, linking it to a child serial number

        :param work_order_id: The ID of the work order associated with the consumption
        :type work_order_id: int
        :param from_serial_number: The serial number being consumed
        :type from_serial_number: SerialNumber
        :param asset_id: The asset ID associated with the consumption
        :type asset_id: int
        :param user: The user performing the consume action
        :type user: str
        """
        # Define the query
        query = '''
            INSERT INTO [mes].[Item_Consumption] (
                [ItemVersionId],
                [WorkOrderId],
                [ToSerialNoId],
                [FromSerialNoId],
                [ItemReasonId],
                [Qty],
                [FromAssetId],
                [ToAssettId],
                [CreatedBy],
                [EditedBy]
            )
            VALUES (
                ?,
                ?,
                ?,
                ?,
                1,
                1,
                ?,
                ?,
                ?,
                ?
            )
        '''
        # Define the arguments
        args = [
            from_serial_number.item_version_id,
            work_order_id,
            self.id,
            from_serial_number.id,
            asset_id,
            asset_id,
            user,
            user
        ]
        try:
            # Run the query
            system.db.runPrepUpdate(query, args, 'MESDB')
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())

    def update_state(self, state_name, user):
        """
        Update the state of a serial number

        :param state_name: The new state name for the serial number
        :type state_name: str
        :param user: The user performing the update
        :type user: str
        """
        # Define the query
        query = '''
            UPDATE [mes].[Item_SerialNo]
            SET ItemSerialNoStateId = (
                SELECT ItemSerialNoStateId
                FROM [mes].[Item_SerialNo_State]
                WHERE ItemSerialNoStateName=?
            ),
            [EditedBy]=?
            [EditedAt]=GETDATE()
            WHERE ItemSerialNoId=?
        '''
        # Define the arguments
        args = [state_name, user, self._id]
        try:
            # Run the query
            system.db.runPrepUpdate(query, args, 'MESDB')
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())
