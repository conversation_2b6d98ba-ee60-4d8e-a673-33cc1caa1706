.ia_dateRangePicker {
    color: var(--label);
}

.ia_dateRangePicker--disabled {
    color: var(--label--disabled);
}

.ia_dateRangePicker__calendarBar__dayOfWeekTile {
    color: var(--neutral-90);
    font-size: 0.875rem;
    line-height: 1rem;
    font-weight: 500;
}

.ia_dateRangePicker__calendar__dayTile {
    font-size: 0.875rem;
    line-height: 1rem;
    color: inherit;
    border: 2px solid transparent;
}

.ia_dateRangePicker__calendar__dayTile--fillerTile {
    color: var(--neutral-40);
}

.ia_dateRangePicker__calendar__dayTile--node {
    border: 2px solid var(--callToAction);
    font-weight: 500;
    border-radius: var(--borderRadius);
}

.ia_dateRangePicker__calendar__dayTile--node--disabled {
    border-color: var(--callToAction--disabled);
}

.ia_dateRangePicker__calendar__dayTile--rangeNode {
    background-color: var(--callToAction);
    color: var(--neutral-90);
    font-weight: 500;
    border-radius: var(--borderRadius);
}

.ia_dateRangePicker__calendar__dayTile--range {
    background-color: var(--callToActionHighlight);
    border-radius: 0;
}

.ia_dateRangePicker__calendar__dayTile--outOfRange {
    color: var(--neutral-40);
}

.ia_dateRangePicker__calendar__dayTile:hover:not(.ia_dateRangePicker__calendar__dayTile--fillerTile):not(.ia_dateRangePicker__calendar__dayTile--node):not(.ia_dateRangePicker__calendar__dayTile--rangeNode):not(.ia_dateRangePicker__calendar__dayTile--outOfRange):not(.hasTouch) {
    background-color: rgba(71, 169, 229, 0.3);
}

.ia_dateRangePicker__pagerArrow--disabled {
    color: var(--label--disabled);
}
