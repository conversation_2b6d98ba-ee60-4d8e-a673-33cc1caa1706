from java.lang import Exception as JavaException
import system.date
import system.tag
import system.db
import system.dataset

SPROC_RESULTS = {
    'result': False, 'result_code': 0, 'result_message': 'No SPROC Execution', 'resp_series': 0, 'resp_type': 0, 'resp_status': 0, 'sp_duration': 0, 'txn_sn': ''
}


def read_txn_data(txn_inputs, base_tagpath):
    tags_read = system.tag.readBlocking([base_tagpath+i for i in txn_inputs])
    txn_data = {str(txn_inputs[e]).split('/')[-1]: tr.value for e, tr in enumerate(tags_read)}
    return txn_data


def parse_mes_barcode(barcode):
    bc_list = barcode.split('|')
    # If new format (with 6 parts), combine the last two elements
    if len(bc_list) == 6:
        bc_list[4] = bc_list[4] + bc_list[5]
        bc_list = bc_list[:5]  # Remove the last element (now combined)
    # Take the last element of the list and perform required formatting.
    barcode = str(bc_list[-1].strip().replace(' ', '').replace('\x00', ''))
    return barcode


# TXN Execution
def execute_txn(base_tagpath, txn_start, read_error, txn_data, call):
    sproc_results = dict(SPROC_RESULTS)
    #######################
    # SPROC
    if not read_error:
        sproc_results = dict(execute_sproc(call))
    else:
        sproc_results['ResultCode'] = 1015
        sproc_results['ResultMessage'] = "Values missing in PLC Data or Null Inputs"

    path_alarm = system.tag.readBlocking(base_tagpath+"/Inputs/PathALARM")[0].value
    alarm = system.tag.readBlocking(path_alarm)[0].value

    # Don't return response if transaction timed out.
    if alarm != 102:
        #######################
        # RESPONSE
        write_response(txn_data['PathRESP'], sproc_results)
        #######################
        # RSD
        determine_rsd(sproc_results['result'], sproc_results['result_message'],
                      sproc_results['result_code'], txn_data['PathRSD'], base_tagpath)
    #######################
    # TXN END
    exclude_keys = {'PathRSD', 'PathRESP', 'PathDATA'}
    data_payload = {k: txn_data[k] for k in set(list(txn_data.keys())) - exclude_keys}
    txn_log_entry(txn_data['ProcessStep'], base_tagpath, sproc_results, data_payload, txn_start)


def execute_sproc(call):
    try:
        sproc_results = dict(SPROC_RESULTS)
        # Sproc Execution
        system.db.execSProcCall(call)
        db_results = system.dataset.toPyDataSet(call.getResultSet())[0]
        sproc_results['result'] = db_results['result']
        sproc_results['result_code'] = db_results['result_code']
        sproc_results['result_message'] = db_results['result_message']
        sproc_results['resp_series'] = db_results['resp_series']
        sproc_results['resp_type'] = db_results['resp_type']
        sproc_results['resp_status'] = db_results['resp_status']
        sproc_results['sp_duration'] = db_results['sp_duration']
        sproc_results['txn_sn'] = db_results['txn_sn']
        return sproc_results
    except JavaException, e:
        raise Exception(str(e))


def write_response(path_resp, sproc_results):
    try:
        if sproc_results["Success"]:
            system.tag.writeBlocking(
                [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status'],
                [sproc_results['resp_series'], sproc_results['resp_type'], sproc_results['resp_status']]
            )
        else:
            # Sending the result code (failure) instead of the part status
            system.tag.writeBlocking(
                [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status'],
                [sproc_results['resp_series'], sproc_results['resp_type'], sproc_results['result_code']]
            )
    except:
        pass

# For Handshake_v3 transactions


def determine_rsd(success, message, code, path_rsd, base_tagpath):
    rsd_value = 2
    failed = 1
    complete = 0

    if success:
        rsd_value = 1
        failed = 0
        message = ''
        code = -1
        complete = 1

    paths = [base_tagpath + '/Status/Trigger', base_tagpath + '/Status/Failed', base_tagpath + '/Status/FailureMessage',
             base_tagpath + '/Status/FailureCode', path_rsd, base_tagpath + '/Status/Complete']
    #Trigger, Failed, FailMsg, FailCode, RSD, Complete
    vals = [0, failed, message, code, rsd_value, complete]
    system.tag.writeBlocking(paths, vals)


def txn_log_entry(process_step, base_tagpath, txn_output, data_payload, txn_start):
    try:
        duration = get_txn_start() - txn_start
        line = None

        try:
            line = int(base_tagpath.split("Line", 1)[1][0])
        except ValueError:
            pass

        call = system.db.createSProcCall("[txn_sp].[TXN_Log_Entry]", "REVMES")
        call.registerInParam("p_ProcessStep", system.db.INTEGER, process_step or 0)
        call.registerInParam("p_TagPath", system.db.NVARCHAR, base_tagpath)
        call.registerInParam("p_Result", system.db.BIT, txn_output['result'])
        call.registerInParam("p_FailureCode", system.db.INTEGER, txn_output['result_code'])
        call.registerInParam("p_TxnOutput", system.db.NVARCHAR, str(txn_output))
        call.registerInParam("p_DataPayload", system.db.NVARCHAR, str(data_payload))
        call.registerInParam("p_Duration_ms", system.db.INTEGER, duration)
        call.registerInParam("p_SPDuration_ms", system.db.INTEGER, txn_output['sp_duration'])
        call.registerInParam("p_Line", system.db.INTEGER, line)
        call.registerInParam("p_TxnSerialNumber", system.db.NVARCHAR, txn_output['txn_sn'])
        system.db.execSProcCall(call)

    except JavaException, e:
        print Exception(str(e))
        pass


# For Handshake_v3 transactions
def handle_uncaught_exceptions(ex, base_tagpath):
    print(repr(ex))
    DataAccess.Log.LogError(repr(ex), functionName=base_tagpath, application='transactionTriggerLogic', user='MES')
    system.tag.writeBlocking([
        base_tagpath+"/Status/Complete",
        base_tagpath+"/Status/Failed",
        base_tagpath+"/Status/FailureMessage",
        base_tagpath+"/Status/FailureCode",
        base_tagpath+"/Status/InProcess",
        base_tagpath+'/Status/Trigger'],

        [0, 1, repr(ex),  9999, False, False])
