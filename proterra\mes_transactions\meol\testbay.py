from java.lang import Exception as JavaException
from proterra.mes_transactions import txn, util
import system.date
import system.util


def moduleIngress(baseTagPath):
    try:
        # Function Variables
        fnlogger = system.util.getLogger('REVMES.MEOL.TestBay.moduleIngress')
        txn_start = system.date.toMillis(system.date.now())
        params = []
        readError = False
        # MES Response
        sprocResults = {
            'Success': False, 'ResultCode': 0, 'ResultMessage': '', 'RespSeries': 0, 'RespType': 0, 'RespStatus': 0, 'SPDuration': 0, 'TxnSerialNumber': ''
        }
        spineSerialNumber = ''

        ###################################
        ######### DATA COLLECTION #########
        try:
            txnInputValues = {}
            # Reading the parameters and inputs
            # This is a tuple, in order to maintain the correct order
            txnInputs = [
                ('ItemVersionId', '/Inputs/ItemVersionId'), ('WorkOrderId', '/Inputs/WorkOrderId'), ('AssetId', '/Inputs/AssetId'), ('UserName',
                                                                                                                                     '/Inputs/UserName'), ('PathRESP', '/Inputs/PathRESP'), ('PathRSD', '/Inputs/PathRSD'), ('ProcessStep', "/Inputs/ProcessStep"), ('PLCData', '/PLCData')
            ]
            txnInputValues = txn.getTXNInputValues(txnInputs, baseTagPath)
            for txnInput in txnInputValues:
                if txnInputValues[txnInput] is None:
                    raise ValueError("Found null value at "+txnInput)

            # Retrieving the Spine SN from PLCData and load recipe.
            if str(txnInputValues['PLCData']) != "{}":
                spineSerialNumber = util.parseBarcode_Last(txnInputValues['PLCData']["Spine_Barcode"])
                system.tag.writeBlocking(baseTagPath+'/SpineSerialNumber', spineSerialNumber)

        except Exception as e1:
            readError = True
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = str(e1)

        #######################
        # SPROC
        if not readError:
            sproc = """
				EXECUTE [REVMES].[txn].sp_MEOL_ModuleMovement
			       @p_ItemVersionId = ?
			      ,@p_WorkOrderId = ?
			      ,@p_SpineSerialNumber = ?
			      ,@p_AssetId = ?
			      ,@p_Username = ?
			      ,@p_ProcessStep = ?
			"""
            params = [
                txnInputValues['ItemVersionId'],
                txnInputValues['WorkOrderId'],
                spineSerialNumber,
                txnInputValues['AssetId'],
                txnInputValues['UserName'],
                txnInputValues['ProcessStep']
            ]
            try:
                # Sproc Execution
                results = util.convertResultToJSON(system.db.runPrepQuery(sproc, params, 'MESDB'))
                sprocResults['Success'] = results['Result'] == 'Success'
                sprocResults['ResultCode'] = results['ResultCode']
                sprocResults['ResultMessage'] = results['ResultMessage']
                sprocResults['RespSeries'] = results['RespSeries']
                sprocResults['RespType'] = results['RespType']
                sprocResults['RespStatus'] = results['RespStatus']
                sprocResults['SPDuration'] = results['SPDuration']
                sprocResults['SPDuration'] = results['SPDuration']
                sprocResults['TxnSerialNumber'] = results['TxnSerialNumber']
            except JavaException as e:
                raise Exception("Error with DB Stored Procedure. Check parameters.")

        #######################
        # RESPONSE
        txn.writeResponse(txnInputValues['PathRESP'], sprocResults)
        if sprocResults['Success']:
            # added assetid to interface, PCM, 20230104
            MES.MEOL.Recipe.GetRecipe(txnInputValues['ItemVersionId'], txnInputValues['PathRESP'], 6)
        #######################
        # RSD
        txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                         sprocResults['ResultCode'], txnInputValues['PathRSD'], baseTagPath)
        #######################
        # TXN END
        txn.txnLogEntry(txnInputValues['ProcessStep'], baseTagPath, sprocResults, params, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def testResults(baseTagPath):
    try:
        # Function Variables
        fnlogger = system.util.getLogger('REVMES.MEOL.TestBay.testResults')
        txn_start = system.date.toMillis(system.date.now())
        params = []
        paramsLog = []
        readError = False
        # MES Response
        sprocResults = {
            'Success': False, 'ResultCode': 0, 'ResultMessage': '', 'RespSeries': 0, 'RespType': 0, 'RespStatus': 0, 'SPDuration': 0, 'TxnSerialNumber': ''
        }
        modulePass = False
        ###################################
        ######### DATA COLLECTION #########
        try:
            txnInputValues = {}
            # Reading the parameters and inputs
            # This is a tuple, in order to maintain the correct order
            txnInputs = [
                ('ItemVersionId', '/Inputs/ItemVersionId'), ('WorkOrderId', '/Inputs/WorkOrderId'), ('AssetId', '/Inputs/AssetId'), ('UserName', '/Inputs/UserName'), ('PathRESP',
                                                                                                                                                                       '/Inputs/PathRESP'), ('PathRSD', '/Inputs/PathRSD'), ('ProcessStep', "/Inputs/ProcessStep"), ('PathDATA', '/Inputs/PathDATA'), ('PLCData', '/PLCData')
            ]
            txnInputValues = txn.getTXNInputValues(txnInputs, baseTagPath)
            for txnInput in txnInputValues:
                if txnInputValues[txnInput] is None:
                    raise ValueError("Found null value at "+txnInput)

            # Retrieving the Spine SN from PLCData.
            if str(txnInputValues['PLCData']) != "{}":
                spineSerialNumber = util.parseBarcode_Last(txnInputValues['PLCData']["MaterialBarcode1"])
                system.tag.writeBlocking(baseTagPath+'/SpineSerialNumber', spineSerialNumber)

                testResults = {}
                testRslt = {}

                for i in range(0, 3):
                    try:
                        testResults = {}
                        testResults['FullMEOL'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'], MES.MEOL.Constants.FullMEOL)
                        testResults['BMB1'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'] + "/TstBmb1", MES.MEOL.Constants.Bmb)
                        testResults['BMB2'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'] + "/TstBmb2", MES.MEOL.Constants.Bmb)
                        testResults['ISO1'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'] + "/TstIso1", MES.MEOL.Constants.Iso)
                        testResults['ISO2'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'] + "/TstIso2", MES.MEOL.Constants.Iso)
                        testResults['HIPOT'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'] + "/TstHiPot", MES.MEOL.Constants.HiPot)
                        testResults['IMPDNC'] = Logic.Parse.GetDictFromParamList(
                            txnInputValues['PathDATA'] + "/TstImpdnc", MES.MEOL.Constants.Impdnc)

                        testRslt = {}
                        modulePass = True
                        for test in testResults:
                            if test == "FullMEOL":
                                testRslt["FullMEOL"] = testResults["FullMEOL"]["ModuleTstRsltCode"]
                            else:
                                testRslt[test] = testResults[test]['TstRslt/TstRsltCode']
                            if not testRslt[test] == 1:
                                modulePass = False

                        # Validation needed to prevent missed test results
                        if testRslt["FullMEOL"] == 0:
                            raise Exception("Missing FullMEOL test results")
                        if testRslt["FullMEOL"] == 1 and modulePass == False:
                            raise Exception("Missing passing test results")
                        system.tag.writeBlocking(baseTagPath+"/Status/_MES_Log", "Valid Test Results on loop "+str(i))
                    except Exception as ex:
                        system.tag.writeBlocking(baseTagPath+"/Status/_MES_Log", "Error at loop "+str(i)+":"+str(ex))
                        continue
                    break
                # Validation needed to prevent missed test results
                if testRslt["FullMEOL"] == 0:
                    raise Exception("Missing FullMEOL test results")
                if testRslt["FullMEOL"] == 1 and modulePass == False:
                    raise Exception("Missing passing test results")

        except Exception as e1:
            readError = True
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = (e1)

        #######################
        # SPROC
        if not readError:
            sproc = """
				EXECUTE [REVMES].[txn].sp_MEOL_TestBay_TestResults
			       @p_ItemVersionId = ?
			      ,@p_WorkOrderId = ?
			      ,@p_SpineSerialNumber = ?
			      ,@p_AssetId = ?
			      ,@p_Username = ?
			      ,@p_ProcessStep = ?
				  ,@p_BMB1Rslt = ? ,@p_BMB1Results = ?
		  	      ,@p_BMB2Rslt = ?, @p_BMB2Results = ?
			      ,@p_ISO1Rslt = ?, @p_ISO1Results = ?
			      ,@p_ISO2Rslt = ?, @p_ISO2Results = ?
			      ,@p_HIPOTRslt = ?, @p_HIPOTResults = ?
			      ,@p_ImpdncRslt = ?, @p_ImpdncResults = ?
  			      ,@p_FullMEOLRslt = ?, @p_FullMEOLResults = ?
			      ,@p_ModulePass = ?
			"""
            params = [
                txnInputValues['ItemVersionId'], txnInputValues['WorkOrderId'], spineSerialNumber, txnInputValues['AssetId'], txnInputValues['UserName'], txnInputValues['ProcessStep'], testRslt['BMB1'], str(testResults['BMB1']), testRslt['BMB2'], str(
                    testResults['BMB2']), testRslt['ISO1'], str(testResults['ISO1']), testRslt['ISO2'], str(testResults['ISO2']), testRslt['HIPOT'], str(testResults['HIPOT']), testRslt['IMPDNC'], str(testResults['IMPDNC']), testRslt['FullMEOL'], str(testResults['FullMEOL']), modulePass
            ]
            paramsLog = [
                txnInputValues['ItemVersionId'], txnInputValues['WorkOrderId'], spineSerialNumber, txnInputValues['AssetId'], txnInputValues['UserName'], txnInputValues[
                    'ProcessStep'], testRslt['BMB1'], testRslt['BMB2'], testRslt['ISO1'], testRslt['ISO2'], testRslt['HIPOT'], testRslt['IMPDNC'], testRslt['FullMEOL'], modulePass
            ]

            try:
                # Sproc Execution
                results = util.convertResultToJSON(system.db.runPrepQuery(sproc, params, 'MESDB'))
                sprocResults['Success'] = results['Result'] == 'Success'
                sprocResults['ResultCode'] = results['ResultCode']
                sprocResults['ResultMessage'] = results['ResultMessage']
                sprocResults['RespSeries'] = results['RespSeries']
                sprocResults['RespType'] = results['RespType']
                sprocResults['RespStatus'] = results['RespStatus']
                sprocResults['SPDuration'] = results['SPDuration']
                sprocResults['TxnSerialNumber'] = results['TxnSerialNumber']
            except JavaException as e:
                raise Exception("Error with DB Stored Procedure. Check parameters."+str(e))
        #######################
        # RESPONSE
        txn.writeResponse(txnInputValues['PathRESP'], sprocResults)
        #######################
        # RSD
        txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                         sprocResults['ResultCode'], txnInputValues['PathRSD'], baseTagPath)
        #######################
        # TXN END
        txn.txnLogEntry(txnInputValues['ProcessStep'], baseTagPath, sprocResults, paramsLog, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)
