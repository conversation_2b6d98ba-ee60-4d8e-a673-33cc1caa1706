.ia_horizontalMenuComponent {
    border-top: var(--containerBorder);
    border-bottom: var(--containerBorder);
}

.ia_horizontalMenuComponent__item {
    color: var(--neutral-70);
    background-color: var(--container);
}

.ia_horizontalMenuComponent__item__icon {
    fill: currentColor;
}

.ia_horizontalMenuComponent__item--disabled {
    color: var(--neutral-50);
}

.ia_horizontalMenuComponent__item__expandIcon {
    fill: var(--neutral-70);
}

.ia_horizontalMenuComponent__item:not(.ia_horizontalMenuComponent__item--active):not(.ia_horizontalMenuComponent__item--disabled):hover {
    filter: brightness(0.9);
}

.ia_horizontalMenuComponent__item--active {
    color: var(--neutral-90);
}

.ia_horizontalMenuComponent__sideNav__iconContainer {
    background-color: var(--neutral-50);
}

.ia_horizontalMenuComponent__modal {
    border: none;
    border-top: var(--containerBorder);
    box-shadow: none;
    background-color: transparent;
}

.ia_horizontalMenuComponent__modal .ia_horizontalMenuComponent__item {
    border-bottom: var(--containerBorder);
}

.ia_horizontalMenuComponent__item__leftItem {
    border-left: 3px solid transparent;
}

.ia_horizontalMenuComponent__item__leftItem--active {
    border-left: 3px solid var(--callToAction);
}

.ia_horizontalMenuComponent__item__rightItem {
    border-right: 3px solid transparent;
}

.ia_horizontalMenuComponent__item__rightItem--active {
    border-right: 3px solid var(--callToAction);
}

.ia_horizontalMenuComponent__sideNavContainerLeft {
    /* Provides a fade effect above menu items that are not fully in view */
    background: linear-gradient(to right, var(--container), 50%, transparent 100%);
}

.ia_horizontalMenuComponent__sideNavContainerRight {
    /* Provides a fade effect above menu items that are not fully in view */
    background: linear-gradient(to right, transparent, var(--container) 50%, var(--container) 100%);
}
