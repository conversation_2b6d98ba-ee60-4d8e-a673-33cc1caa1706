import platform
from proterra.transactions import TransactionBase
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner


class TransactionTests(TestCase):
    def test1(self):
        pass


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            TransactionTests,
            TransactionTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
