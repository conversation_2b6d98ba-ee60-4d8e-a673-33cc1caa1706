/** Misc */
.ia_symbolComponent__liquid {
    fill: var(--info);
    fill-opacity: 0.7;
    stroke-width: 2px;
}

.ia_symbolComponent__liquid--warning {
    fill: var(--error);
}

/** Animations */
@keyframes ia_symbolComponent__animate--alternateFill {
    0% {
      fill: var(--symbolFill--default);
    }
    50% {
      fill: var(--symbolFillAnimation--default);
    }
    100% {
      fill: var(--symbolFill--default);
    }
}

@keyframes ia_symbolComponent__animate--alternateFillAlt {
    0% {
      fill: var(--symbolFillAnimation--default);
    }
    50% {
      fill: var(--symbolFill--default);
    }
    100% {
      fill: var(--symbolFillAnimation--default);
    }
}

@keyframes ia_symbolComponent__animate--alternateFill--running {
    0% {
      fill: var(--symbolFill--running);
    }
    50% {
      fill: var(--symbolFillAnimation--running);
    }
    100% {
      fill: var(--symbolFill--running);
    }
}

@keyframes ia_symbolComponent__animate--alternateFillAlt--running {
    0% {
      fill: var(--symbolFillAnimation--running);
    }
    50% {
      fill: var(--symbolFill--running);
    }
    100% {
      fill: var(--symbolFillAnimation--running);
    }
}

@keyframes ia_symbolComponent__animate--rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
}

@keyframes ia_symbolComponent__animate--rotate3dYAxis {
    0% {
      transform: rotate3d(0, 1, 0, 0deg);
    }
    100% {
      transform: rotate3d(0, 1, 0, 360deg);
    }
}

@keyframes ia_symbolComponent__animate--faulted {
    0% {
      fill: var(--symbolFill--running);
    }
    50% {
      fill: var(--symbolFill--faulted);
    }
    100% {
      fill: var(--symbolFill--running);
    }
}

@keyframes ia_symbolComponent__animate--failedToOpen {
    0% {
      fill: var(--symbolFill--running);
    }
    50% {
      fill: var(--symbolFill--faulted);
    }
    100% {
      fill: var(--symbolFill--running);
    }
}

@keyframes ia_symbolComponent__animate--failedToClose {
    0% {
      fill: var(--symbolFill--stopped);
    }
    50% {
      fill: var(--symbolFill--faulted);
    }
    100% {
      fill: var(--symbolFill--stopped);
    }
}

/** States - To create a custom state copy the default, then post-fix "--{stateName}" ie "--running", finally modify animation/colors of new state as desired */
/** Default */
.ia_symbolComponent {
    fill: var(--symbolFill--default);
    stroke: var(--symbolStroke--default);
}

.ia_symbolComponent__altFill {
    fill: var(--symbolStroke--default);
}

.ia_symbolComponent__inside {
    fill: var(--symbolFill--stopped);
}

.ia_symbolComponent__animate--alternateFill {
    animation: none;
}

.ia_symbolComponent__animate--alternateFillAlt {
    animation: none
}

.ia_symbolComponent__animate--rotate {
    animation: none;
}

.ia_symbolComponent__animate--rotate3dYAxis {
    animation: none;
}

/** Running */
.ia_symbolComponent--running {
    fill: var(--symbolFill--running);
    stroke: var(--symbolStroke--running);
}

.ia_symbolComponent__altFill--running {
    fill: var(--symbolStroke--running);
}

.ia_symbolComponent__animate--alternateFill--running {
    animation: ia_symbolComponent__animate--alternateFill--running linear infinite;
}

.ia_symbolComponent__animate--alternateFillAlt--running {
    animation: ia_symbolComponent__animate--alternateFillAlt--running linear infinite;
}

.ia_symbolComponent__animate--rotate--running {
    animation: ia_symbolComponent__animate--rotate linear infinite;
}

.ia_symbolComponent__animate--rotate3dYAxis--running {
    animation: ia_symbolComponent__animate--rotate3dYAxis linear infinite;
}

/** Stopped */
.ia_symbolComponent--stopped {
    fill: var(--symbolFill--stopped);
    stroke: var(--symbolStroke--stopped);
    animation: ia_symbolComponent__animate--stopped linear infinite;
}

.ia_symbolComponent__altFill--stopped {
    fill: var(--symbolStroke--stopped);
}

/** Faulted */
.ia_symbolComponent--faulted {
    fill: var(--symbolFill--faulted);
    stroke: var(--symbolStroke--faulted);
    animation: ia_symbolComponent__animate--faulted linear infinite;
}

.ia_symbolComponent__altFill--faulted {
    fill: var(--symbolStroke--faulted);
}


/** Valve States **/
/** Open */
.ia_symbolComponent--open {
    fill: var(--symbolFill--running);
    stroke: var(--symbolStroke--running);
}

.ia_symbolComponent__altFill--open {
    fill: var(--symbolStroke--running);
}

/** Failed to Open */
.ia_symbolComponent--failedToOpen {
    fill: var(--symbolFill--default);
    stroke: var(--symbolStroke--faulted);
    animation: ia_symbolComponent__animate--failedToOpen linear infinite;
}

.ia_symbolComponent__altFill--failedToOpen {
    fill: var(--symbolStroke--faulted);
}

/** Partially Closed */
.ia_symbolComponent--partiallyClosed {
    fill: var(--symbolFill--stopped);
    stroke: var(--symbolStroke--running);
}

.ia_symbolComponent__altFill--partiallyClosed {
    fill: var(--symbolStroke--running);
}

/** Partially Closed - Left Pipe */
.ia_symbolComponent__defaultFill--partiallyClosed {
    fill: var(--symbolFill--running);
}

.ia_symbolComponent__defaultFill--reverseFlow--partiallyClosed {
    fill: var(--symbolFill--stopped);
}

/* Partially Closed - Right Pipe */
.ia_symbolComponent__defaultFillAlt--partiallyClosed {
    fill: var(--symbolFill--stopped);
}

.ia_symbolComponent__defaultFillAlt--reverseFlow--partiallyClosed {
    fill: var(--symbolFill--running);
}

/** Closed */
.ia_symbolComponent--closed {
    fill: var(--symbolFill--stopped);
    stroke: var(--symbolStroke--stopped);
}

.ia_symbolComponent__altFill--closed {
    fill: var(--symbolStroke--stopped);
}

/** Failed to Close */
.ia_symbolComponent--failedToClose {
    fill: var(--symbolFill--stopped);
    stroke: var(--symbolStroke--faulted);
    animation: ia_symbolComponent__animate--failedToClose linear infinite;
}

.ia_symbolComponent__altFill--failedToClose {
    fill: var(--symbolStroke--faulted);
}
