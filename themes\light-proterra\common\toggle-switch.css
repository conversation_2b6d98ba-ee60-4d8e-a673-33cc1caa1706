.ia_toggleSwitch__track {
    background-color: var(--toggleSwitch--unselected);
    filter: invert(25%);
    border-radius: 8px;
    transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.ia_toggleSwitch__track--selected {
    background-color: var(--toggleSwitch--selected);
    opacity: 0.25;
    filter: none;
}

.ia_toggleSwitch__track--disabled {
    background-color: var(--input--disabled) !important;
    opacity: 0.5;
}

.ia_toggleSwitch__thumb {
    border-radius: 50%;
    background-color: var(--toggleSwitch--unselected);
    border: var(--containerBorder);
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.12);
    transition: left 200ms cubic-bezier(0.4, 0, 0.2, 1), left 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.ia_toggleSwitch__thumb--selected {
    background-color: var(--toggleSwitch--selected);
    border: none;
}

.ia_toggleSwitch__thumb:hover {
    border: 1px solid var(--callToAction);
}

.ia_toggleSwitch__thumb--selected:hover {
    filter: brightness(1.2);
}

.ia_toggleSwitch__thumb--disabled {
    background-color: var(--input--disabled) !important;
    border: none;
}

.ia_toggleSwitch__thumb--disabled:hover {
    filter: none;
    border: none;
}
