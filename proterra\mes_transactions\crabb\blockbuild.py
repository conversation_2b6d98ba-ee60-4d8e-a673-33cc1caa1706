from proterra.mes_transactions import txn, util
import system.date
import system.util


def preblockTransferToPallet(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''
        blockSerialNumber = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/ProcessStep', '/PalletSerialNumber', '/BlockSerialNumber'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle

            palletSerialNumber = util.parseBarcode_Last(txnData["PalletSerialNumber"])
            blockSerialNumber = txnData["BlockSerialNumber"]
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_CRABB_BB_PreBlockTransferToPallet]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_BlockSerialNumber", system.db.NVARCHAR, blockSerialNumber)
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN_CRABB(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        system.tag.writeBlocking([baseTagPath+"/Status/_MES_Log"], ['Error'+repr(ex)])
        pass


def CRABB_PreblockTransferToPallet(tagpath):
    #	set up tag paths
    workOrderIdPath = tagpath + '/WorkOrderId'
    itemVersionIdPath = tagpath + '/ItemVersionId'
    assetIdPath = tagpath + '/Inputs/AssetId'
    serialNumberPath = tagpath + '/SerialNumber'
    palletNumberPath = tagpath + '/PalletNumber'

    # Read Values
    readValues = system.tag.readBlocking(
        [workOrderIdPath, itemVersionIdPath, assetIdPath, serialNumberPath, palletNumberPath])
    workOrderId = readValues[0].value
    itemVersionId = readValues[1].value
    assetId = readValues[2].value
    serialNumber = readValues[3].value
    palletNumber = readValues[4].value
    palletSerialNumber = palletNumber.split('|')[2]
    # Pull username from authentication tag
    username = MES.Validation.getTransactionUserName(tagpath)

    callPreBlockTransferToPallet = system.db.createSProcCall("[txn].[sp_CRABB_BB_PreBlockTransferToPallet]", "REVMES")
    callPreBlockTransferToPallet.registerInParam("p_serialNumber", system.db.VARCHAR, serialNumber)
    callPreBlockTransferToPallet.registerInParam("p_palletNumber", system.db.VARCHAR, palletSerialNumber)
    callPreBlockTransferToPallet.registerInParam("p_userName", system.db.VARCHAR, username)
    callPreBlockTransferToPallet.registerInParam("p_assetId", system.db.INTEGER, assetId)
    callPreBlockTransferToPallet.registerInParam("p_workorderId", system.db.BIGINT, workOrderId)
    callPreBlockTransferToPallet.registerInParam("p_itemVersionId", system.db.BIGINT, itemVersionId)
    system.db.execSProcCall(callPreBlockTransferToPallet)
    result = callPreBlockTransferToPallet.getResultSet()

    # return result
    MES.Validation.UpdateStatus(result, tagpath)
