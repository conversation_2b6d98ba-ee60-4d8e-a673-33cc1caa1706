.ia_dropdown {
    background-color: var(--input);
    border: var(--containerBorder);
    border-radius: var(--borderRadiusInput);
    color: var(--neutral-80);
    font-size: 0.875rem;
    fill: var(--icon);
}

.ia_dropdown__placeholder {
    color: var(--label--disabled);
}

.ia_dropdown__valuePill {
    border-radius: var(--borderRadius);
    background-color: var(--containerNested);
    overflow: hidden;
}

.ia_dropdown__removeValueIcon {
    fill: currentColor;
}

.ia_dropdown__removeValue--focused,
.ia_dropdown__removeValue:hover {
    background-color: var(--red-30);
}

.ia_dropdown__removeValue:hover > svg {
    fill: var(--red-60);
}

.ia_dropdown__search {
    color: inherit;
    font-size: 0.875rem;
}

.ia_dropdown--active, .ia_dropdown--focused {
    outline: 1px solid var(--callToAction--hover);
}

.ia_dropdown--disabled {
    color: var(--label--disabled);
    background-color: var(--input--disabled);
    cursor: not-allowed;
    fill: var(--label--disabled);
}

.ia_dropdown--disabled:focus,
.ia_dropdown__search:focus {
    outline: none;
}

.ia_dropdown--error:not(.ia_dropdown--disabled) {
    color: var(--error);
}

.ia_dropdown__optionsModal {
    background-color: var(--input);
    border-radius: var(--borderRadiusInput);
    font-size: 0.875rem;
}

.ia_dropdown__option {
    color: var(--label);
}

.ia_dropdown__option--focused:not(.ia_dropdown__option--selected),
.ia_dropdown__option--focused:not(.ia_dropdown__option--disabled) {
    background-color: var(--callToAction--activeAlt);
    color: var(--label);
}

.ia_dropdown__option.ia_dropdown__option--selected {
    background-color: var(--callToAction--hover);
}

.ia_dropdown__option.ia_dropdown__option--disabled {
    color: var(--label--disabled);
    cursor: not-allowed;
}

.ia_dropdown__option__noResults {
   color: var(--input--disabled);
   pointer-events: none;
}

.ia_dropdown__clearValueIcon:hover {
    fill: var(--icon--disabled);
}
