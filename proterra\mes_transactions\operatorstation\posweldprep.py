from proterra.mes_transactions import txn, util
import system.date
import system.util


def validation(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''
        scannedSerialNumber = ''
        foilSerialNumber = ''
        fullFoilSerialNumber = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "PalletID" in txnData["PLCData"].keys() and "MaterialBarcode1" in txnData["PLCData"].keys():
                palletSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["PalletID"])
                scannedSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["MaterialBarcode1"])
                if "MaterialBarcode2" in txnData["PLCData"].keys():
                    foilSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["MaterialBarcode2"]).replace("	", '')
                    fullFoilSerialNumber = txnData["PLCData"]["MaterialBarcode2"].replace("	", '')
                # Write values to UDT tags
                system.tag.writeBlocking(
                    [baseTagPath+"/PalletSerialNumber", baseTagPath+"/ScannedSerialNumber", baseTagPath +
                        "/FoilSerialNumber"], [palletSerialNumber, scannedSerialNumber, foilSerialNumber]
                )
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_OperatorStation_PosWeldPrep_Validation]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_ScannedSerialNumber", system.db.NVARCHAR, scannedSerialNumber)
            call.registerInParam("p_ScannedFoil", system.db.NVARCHAR, foilSerialNumber)
            call.registerInParam("p_FullScannedFoil", system.db.NVARCHAR, fullFoilSerialNumber)
        # Performing standard TXN Actions (Sproc, Response, Log)
        #txn.performTXN(baseTagPath, txn_start, readError, txnData, call)

        sprocResults = dict(txn.SPROC_RESULTS)
        #######################
        # SPROC
        if not readError:
            sprocResults = dict(txn.runSproc(call))
        else:
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = "Values missing in PLC Data or Null Inputs"

        pathALARM = system.tag.readBlocking(baseTagPath+"/Inputs/PathALARM")[0].value
        alarm = system.tag.readBlocking(pathALARM)[0].value

        if alarm != 102:
            #######################
            # RESPONSE
            txn.writeResponse(txnData['PathRESP'], sprocResults)
            #######################
            # RSD
            txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                             sprocResults['ResultCode'], txnData['PathRSD'], baseTagPath)
        #######################
        # TXN END
        exclude_keys = {'PathRSD', 'PathRESP', 'PathDATA'}
        dataPayload = {k: txnData[k] for k in set(list(txnData.keys())) - exclude_keys}
        txn.txnLogEntry(txnData['ProcessStep'], baseTagPath, sprocResults, dataPayload, txn_start)

        #######################
        # UPDATE UI
        if sprocResults['Success'] or sprocResults['RespStatus'] == 1:
            uiTagPath = baseTagPath.rsplit('/', 1)[0] + '/UI/AwaitingSend'
            system.tag.writeBlocking([uiTagPath], [False])

    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def gate(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "PalletID" in txnData["PLCData"].keys():
                palletSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["PalletID"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/PalletSerialNumber"], [palletSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_OperatorStation_PosWeldPrep_Gate]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def inspection_result(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        barcode = ''
        status = True
        inspection_data = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)
            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "Barcode" in txnData["PLCData"].keys():
                barcode = util.parseBarcode_Last(txnData["PLCData"]["Barcode"])
                status = txnData["PLCData"]["Status"]
                if status == False:
                    inspection_data = txnData["PLCData"]["DataValues"]
                # Write values to UDT tags
                #system.tag.writeBlocking([baseTagPath+"/PalletSerialNumber"], [palletSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_OperatorStation_PosWeldPrep_InspectionResult]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SerialNumber", system.db.NVARCHAR, barcode)
            call.registerInParam("p_Status", system.db.BIT, status)
            call.registerInParam("p_InspectionData", system.db.NVARCHAR, inspection_data)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])

        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
        #txn.txnLogEntry(txnData['ProcessStep'], baseTagPath, '', txnData, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)
