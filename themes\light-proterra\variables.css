:root {
    --white: #FFFFFF;
    --black: #000000;
    --red-20: #FAD4D4;
    --red-30: #F99797;
    --red-50: #F55353;
    --red-60: #DE1B1B;
    --red-90: #661414;

    /* Neutrals */
    --neutral-10: #D7D6FA;   /* neutral-100 */
    --neutral-20: #C0BFE4;   /* neutral-90 */
    --neutral-30: #AAA9CE;   /* neutral-80 */
    --neutral-40: #9392B8;   /* neutral-70 */
    --neutral-50: #7D7CA2;   /* neutral-60 */
    --neutral-60: #66668C;   /* neutral-50 */
    --neutral-70: #504F76;   /* neutral-40 */
    --neutral-80: #393860;   /* neutral-30 */
    --neutral-90: #23224A;   /* neutral-20 */
    --neutral-100: #0D0C33;  /* neutral-10 */

    /* Sequential */
    --seq-1: #AE74E8; /* purple-50 */
    --seq-2: #9659D6; /* purple-60 */
    --seq-3: #7D3CBD; /* purple-70 */
    --seq-4: #642B9E; /* purple-80 */
    --seq-5: #4B2175; /* purple-90 */
    --seq-6: #371C52; /* purple-100 */

    /* Data Viz: Diverging */
    --div-1: #642B9E; /* purple-80 */
    --div-2: #7D3CBD; /* purple-70 */
    --div-3: #9656D6; /* purple-60 */
    --div-4: #AE74E8; /* purple-50 */
    --div-5: #C79BF2; /* purple-40 */
    --div-6: #DABCF7; /* purple-30 */
    --div-7: #EAD9FA; /* purple-20 */
    --div-8: #F5F0FA; /* purple-10 */
    --div-9: #D7FAF8; /* teal-10 */
    --div-10: #83F2EB; /* teal-20 */
    --div-11: #43DED3; /* teal-30 */
    --div-12: #21C2B7; /* teal-40 */
    --div-13: #0EA197; /* teal-50 */
    --div-14: #08827A; /* teal-60 */
    --div-15: #086962; /* teal-70 */
    --div-16: #09524D; /* teal-80 */


    /* Data Viz: Qualitative */
    --qual-1: #7D3CBD; /* purple-70 */
    --qual-2: #229AD6; /* primary-50 */
    --qual-3: #086962; /* teal-70 */
    --qual-4: #B01355; /* magenta-70 */
    --qual-5: var(--red-50); /* red-50 */
    --qual-6: var(--red-90); /* red-90 */
    --qual-7: #038537; /* green-60 */
    --qual-8: #114599; /* blue-80 */
    --qual-9: #ED5393; /* magenta-50 */
    --qual-10: #CF7911; /* bronze-50 */

    --containerRoot: var(--neutral-10); /* the root container, depth 0 */
    --container: var(--neutral-20); /* standard container, depth 1 */
    --containerNested: var(--neutral-30); /* nested container, depth 2 */

    --input: var(--white);
    --input--disabled: var(--neutral-40);

    --icon: var(--neutral-70);
    --icon--hover: var(--neutral-60);
    --icon--disabled: var(--neutral-40);
    --icon--selected: var(--neutral-90);

    --label: var(--neutral-90);
    --label--disabled: var(--neutral-60);

    /* Borders */
    --border: var(--neutral-50);
    --border--disabled: var(--neutral-50);
    --containerBorder: 1px solid var(--border);

    /* Box Shadow */
    --boxShadow1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --boxShadow2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --boxShadow3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    --boxShadow4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
    --boxShadow5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
    --boxShadow--inset: inset 0 0 4px 2px rgba(34, 34, 34, 0.4);

    /* Call to action. */
    --callToAction: #82EB6A; /* primary-60 (green base) */
    --callToActionHighlight: #E6FBE1; /* light green highlight */
    --callToAction--hover: #5FCB46; /* darker green for hover */
    --callToAction--active: #3FA62A; /* even darker green for active */
    --callToAction--activeAlt: #BDF7B3; /* alternative active, light green */
    --callToAction--activeAltInvis: #2E8C1A; /* invisible/alt, deep green */
    --callToAction--disabled: var(--neutral-50);

    /* Functional */
    --error: var(--red-60); /* red-60 */
    --info: #3272D9; /* blue-60 */
    --infoSecondary: #ACCBFC; /* blue-30 */
    --warning: #AD5F00; /* bronze-60 */
    --warningSecondary: #F5BC76; /* bronze-30 */
    --success: #038537; /* green-60 */

    /* Border Radius */
    --borderRadius: 4px;
    --borderRadiusInput: 2px;

    /* Opacity */
    --opacity-25: rgba(0, 0, 0, 0.25); /* black, 50% opacity */
    --opacity-50: rgba(0, 0, 0, 0.50); /* black, 50% opacity */
    --opacity-85: rgba(0, 0, 0, 0.85); /* black, 85% opacity */

    /* Typography */
    --font-NotoSans: 'Noto Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;

    /* Components */
    /* LED Display */
    --indicator: #1EC963; /* green-40 */
    --indicatorOff: #0A2E18; /* green-100 */

    /* Checkbox */
    --checkbox--checked: var(--callToAction);
    --checkbox--unchecked: var(--border);
    --checkbox--indeterminate: var(--callToAction);
    --checkbox--disabled: var(--input--disabled);

    /* Progress Bar */
    --progressLinearTrack--determinate: var(--neutral-10);
    --progressLinearTrack--indeterminate: var(--neutral-10);
    --progressLinearBar--determinate: var(--infoSecondary);
    --progressLinearBar--indeterminate: var(--border);

    /* Toggle Switch */
    --toggleSwitch--selected: var(--callToAction);
    --toggleSwitch--unselected: var(--neutral-10);

    /* Radio */
    --radio--selected: var(--callToAction);
    --radio--unselected: var(--border);
    --radio--disabled: var(--input--disabled);

    /* Piping */
    --pipePrimaryFill: var(--neutral-20);
    --pipeSecondaryFill: #cccccc;
    --pipeStroke: var(--neutral-70);
    --pipeSelectStroke: var(--callToAction);
    
    /* Component Context Menu */
    --contextBackground: var(--white);

    /* Symbols */
    --symbolFill--default: var(--neutral-20);
    --symbolFillAnimation--default: var(--neutral-50);
    --symbolStroke--default: var(--neutral-70);
    --symbolFill--running: var(--white);
    --symbolFillAnimation--running: var(--neutral-50);
    --symbolStroke--running: var(--neutral-70);
    --symbolFill--stopped: var(--neutral-40);
    --symbolStroke--stopped: var(--neutral-70);
    --symbolFill--faulted: #FAB6B6;
    --symbolStroke--faulted: #B80D0D;
}
