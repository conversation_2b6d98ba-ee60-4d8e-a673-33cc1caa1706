from java.lang import Exception as JavaException
from proterra.mes_transactions import txn, util
import system.date
import system.util


def moduleMovement(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        spineSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "MaterialBarcode1" in txnData["PLCData"].keys():
                spineSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["MaterialBarcode1"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/SpineSerialNumber"], [spineSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_MEOL_ModuleMovement]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SpineSerialNumber", system.db.NVARCHAR, spineSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def moduleMovement_REJECT(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        spineSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "MaterialBarcode1" in txnData["PLCData"].keys():
                spineSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["MaterialBarcode1"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/SpineSerialNumber"], [spineSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_MEOL_ModuleMovement_REJECT]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SpineSerialNumber", system.db.NVARCHAR, spineSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def barcodeRead(baseTagPath):
    try:
        # Function Variables
        fnlogger = system.util.getLogger('REVMES.MEOL.Conveyor.barcodeRead')
        txn_start = system.date.toMillis(system.date.now())
        params = []
        readError = False
        # MES Response
        sprocResults = {
            'Success': False, 'ResultCode': 0, 'ResultMessage': '', 'RespSeries': 0, 'RespType': 0, 'RespStatus': 0, 'SPDuration': 0
        }
        spineSerialNumber = ''

        ###################################
        ######### DATA COLLECTION #########
        try:
            txnInputValues = {}
            # Reading the parameters and inputs
            # This is a tuple, in order to maintain the correct order
            txnInputs = [
                ('ItemVersionId', '/Inputs/ItemVersionId'), ('WorkOrderId', '/Inputs/WorkOrderId'), ('AssetId', '/Inputs/AssetId'), ('UserName',
                                                                                                                                     '/Inputs/UserName'), ('PathRESP', '/Inputs/PathRESP'), ('PathRSD', '/Inputs/PathRSD'), ('ProcessStep', "/Inputs/ProcessStep"), ('PLCData', '/PLCData')
            ]
            txnInputValues = txn.getTXNInputValues(txnInputs, baseTagPath)
            for txnInput in txnInputValues:
                if txnInputValues[txnInput] is None:
                    raise ValueError("Found null value at "+txnInput)

            # Retrieving the Spine SN from PLCData
            if str(txnInputValues['PLCData']) != "{}":
                spineSerialNumber = util.parseBarcode_Last(txnInputValues['PLCData']["MaterialBarcode1"])
                system.tag.writeBlocking(baseTagPath+'/SpineSerialNumber', spineSerialNumber)

        except Exception as e1:
            readError = True
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = str(e1)

        #######################
        # SPROC
        if not readError:
            sproc = """
				EXECUTE [REVMES].[txn].sp_MEOL_ModuleMovement
			       @p_ItemVersionId = ?
			      ,@p_WorkOrderId = ?
			      ,@p_SpineSerialNumber = ?
			      ,@p_AssetId = ?
			      ,@p_Username = ?
			      ,@p_ProcessStep = ?
			"""
            params = [
                txnInputValues['ItemVersionId'],
                txnInputValues['WorkOrderId'],
                spineSerialNumber,
                txnInputValues['AssetId'],
                txnInputValues['UserName'],
                txnInputValues['ProcessStep']
            ]
            try:
                # Sproc Execution
                results = util.convertResultToJSON(system.db.runPrepQuery(sproc, params, 'MESDB'))
                sprocResults['Success'] = results['Result'] == 'Success'
                sprocResults['ResultCode'] = results['ResultCode']
                sprocResults['ResultMessage'] = results['ResultMessage']
                sprocResults['RespSeries'] = results['RespSeries']
                sprocResults['RespType'] = results['RespType']
                sprocResults['RespStatus'] = results['RespStatus']
                sprocResults['SPDuration'] = results['SPDuration']
            except JavaException as e:
                raise Exception("Error with DB Stored Procedure. Check parameters.")

        #######################
        # RESPONSE
        txn.writeResponse(txnInputValues['PathRESP'], sprocResults)
        #######################
        # RSD
        txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                         sprocResults['ResultCode'], txnInputValues['PathRSD'], baseTagPath)
        #######################
        # TXN END
        txn.logEntry(txnInputValues['ProcessStep'], baseTagPath, sprocResults, params, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def barcodeRead_REJECT(baseTagPath):
    try:
        # Function Variables
        fnlogger = system.util.getLogger('REVMES.MEOL.Conveyor.barcodeRead_REJECT')
        txn_start = system.date.toMillis(system.date.now())
        params = []
        readError = False
        # MES Response
        sprocResults = {
            'Success': False, 'ResultCode': 0, 'ResultMessage': '', 'RespSeries': 0, 'RespType': 0, 'RespStatus': 0, 'SPDuration': 0
        }
        spineSerialNumber = ''

        ###################################
        ######### DATA COLLECTION #########
        try:
            txnInputValues = {}
            # Reading the parameters and inputs
            # This is a tuple, in order to maintain the correct order
            txnInputs = [
                ('ItemVersionId', '/Inputs/ItemVersionId'), ('WorkOrderId', '/Inputs/WorkOrderId'), ('AssetId', '/Inputs/AssetId'), ('UserName',
                                                                                                                                     '/Inputs/UserName'), ('PathRESP', '/Inputs/PathRESP'), ('PathRSD', '/Inputs/PathRSD'), ('ProcessStep', "/Inputs/ProcessStep"), ('PLCData', '/PLCData')
            ]
            txnInputValues = txn.getTXNInputValues(txnInputs, baseTagPath)
            for txnInput in txnInputValues:
                if txnInputValues[txnInput] is None:
                    raise ValueError("Found null value at "+txnInput)

            # Retrieving the Spine SN from PLCData
            if str(txnInputValues['PLCData']) != "{}":
                spineSerialNumber = util.parseBarcode_Last(txnInputValues['PLCData']["MaterialBarcode1"])
                system.tag.writeBlocking(baseTagPath+'/SpineSerialNumber', spineSerialNumber)

        except Exception as e1:
            readError = True
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = str(e1)

        #######################
        # SPROC
        if not readError:
            sproc = """
				EXECUTE [REVMES].[txn].sp_MEOL_ModuleMovement_REJECT
			       @p_ItemVersionId = ?
			      ,@p_WorkOrderId = ?
			      ,@p_SpineSerialNumber = ?
			      ,@p_AssetId = ?
			      ,@p_Username = ?
			      ,@p_ProcessStep = ?
			"""
            params = [
                txnInputValues['ItemVersionId'],
                txnInputValues['WorkOrderId'],
                spineSerialNumber,
                txnInputValues['AssetId'],
                txnInputValues['UserName'],
                txnInputValues['ProcessStep']
            ]
            try:
                # Sproc Execution
                results = util.convertResultToJSON(system.db.runPrepQuery(sproc, params, 'MESDB'))
                sprocResults['Success'] = results['Result'] == 'Success'
                sprocResults['ResultCode'] = results['ResultCode']
                sprocResults['ResultMessage'] = results['ResultMessage']
                sprocResults['RespSeries'] = results['RespSeries']
                sprocResults['RespType'] = results['RespType']
                sprocResults['RespStatus'] = results['RespStatus']
                sprocResults['SPDuration'] = results['SPDuration']
            except JavaException as e:
                raise Exception("Error with DB Stored Procedure. Check parameters.")

        #######################
        # RESPONSE
        txn.writeResponse(txnInputValues['PathRESP'], sprocResults)
        #######################
        # RSD
        txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                         sprocResults['ResultCode'], txnInputValues['PathRSD'], baseTagPath)
        #######################
        # TXN END
        txn.logEntry(txnInputValues['ProcessStep'], baseTagPath, sprocResults, params, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)
