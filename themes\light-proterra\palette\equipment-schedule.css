.ia_equipmentScheduleComponent {
    border: var(--containerBorder);
    font-size: 0.75rem;
    color: var(--neutral-90);
}

.ia_equipmentScheduleComponent__actionBar,
.ia_equipmentScheduleComponent__spacer,
.ia_equipmentScheduleComponent__groupingCell__backdrop,
.ia_equipmentScheduleComponent__headerCell__backdrop,
.ia_equipmentScheduleComponent__itemList,
.ia_equipmentScheduleComponent__itemList__item,
.ia_equipmentScheduleComponent__groupingHeader {
    background: var(--neutral-30);
}

.ia_equipmentScheduleComponent__actionBar,
.ia_equipmentScheduleComponent__spacer,
.ia_equipmentScheduleComponent__headerCell,
.ia_equipmentScheduleComponent__groupingCell,
.ia_equipmentScheduleComponent__itemList__item,
.ia_equipmentScheduleComponent__itemList__item,
.ia_equipmentScheduleComponent__gridSpace__gridCell,
.ia_equipmentScheduleComponent__gridSpace__gridCell--hover {
    border-bottom: var(--containerBorder);
}

.ia_equipmentScheduleComponent__spacer,
.ia_equipmentScheduleComponent__groupingCell,
.ia_equipmentScheduleComponent__headerCell,
.ia_equipmentScheduleComponent__itemList,
.ia_equipmentScheduleComponent__gridSpace__gridCell,
.ia_equipmentScheduleComponent__gridSpace__gridCell--hover {
    border-right: var(--containerBorder);
}

.ia_equipmentScheduleComponent__gridSpace__gridCell {
    background: var(--container);
}

.ia_equipmentScheduleComponent__gridSpace__gridCell--hover {
    background: var(--neutral-30);
}

.ia_equipmentScheduleComponent__actionBar,
.ia_equipmentScheduleComponent__groupingCell,
.ia_equipmentScheduleComponent__itemList__item {
    font-weight: 700;
}

.ia_equipmentScheduleComponent__headerCell:hover,
.ia_equipmentScheduleComponent__actionButton {
    background: var(--neutral-10);
}

.ia_equipmentScheduleComponent__actionButton {
    border: var(--neutral-50);
    border-radius: var(--borderRadius);
}

.ia_equipmentScheduleComponent__actionBar__dropdown {
    font-size: 0.75rem;
    width: 7rem;
}

.ia_equipmentScheduleComponent__actionButton__icon {
    fill: var(--neutral-90);
}

.ia_equipmentScheduleComponent__add__icon {
    fill: var(--qual-2);
}

.ia_equipmentScheduleComponent__overlappingEvent__backdrop {
    background: var(--neutral-40);
    opacity: 0.5;
}

.ia_equipmentScheduleComponent__overlappingEvent__dropdown {
    color: var(--white);
    background: var(--black);
    font-size: 0.625rem;
    font-weight: 700;
}

.ia_equipmentScheduleComponent__overlappingEvent__dropdown__icon {
    fill: var(--white);
}

.ia_equipmentScheduleComponent__scheduleEvent__progressBar__bar__color {
    background-color: #0C7BB3;
}

.ia_equipmentScheduleComponent__scheduleEvent__progressBar__track__color {
    background-color: #FFFFFF;
}

.ia_equipmentScheduleComponent__tooltip {
    background-color: #000000;
    font-size: 0.625rem;
    color: #FFFFFF;
}

.ia_equipmentScheduleComponent__scheduleEvent {
    background: #7D3CBD;
    color: #FFFFFF;
    font-size: 0.75rem;
    font-weight: normal;
    border-radius: var(--borderRadius);
}

.ia_equipmentScheduleComponent__scheduleEvent__leadTime {
    background-color: #FAD8AF;
}

.ia_equipmentScheduleComponent__resizePlaceHolder,
.ia_equipmentScheduleComponent__movePlaceHolder,
.ia_equipmentScheduleComponent__selectedPlaceHolder {
    border-radius: var(--borderRadius);
}

.ia_equipmentScheduleComponent__resizePlaceHolder {
    border: 2px solid var(--black);
}

.ia_equipmentScheduleComponent__movePlaceHolder {
    border: 2px dashed rgba(0,0,0,0.4);
}

.ia_equipmentScheduleComponent__selectedPlaceHolder {
    border: 2px solid #47A9E5;
}

.ia_equipmentScheduleComponent__downtimeEvent {
    background-color: rgba(222,27,27,0.3);
}

.ia_equipmentScheduleComponent__breakEvent {
    background-color: rgba(10,166,72,0.3);
}
