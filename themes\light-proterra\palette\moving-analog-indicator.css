.ia_movingAnalogIndicatorComponent {
    /* Inherited by all text svg elements */
    font-size: 1rem;
}

.ia_movingAnalogIndicatorComponent__scale {
    fill: var(--container);
    stroke: var(--border);
}

.ia_movingAnalogIndicatorComponent__desiredRange {
    fill: var(--infoSecondary);
    stroke: var(--border);
}

.ia_movingAnalogIndicatorComponent__interlockRange {
    fill: var(--neutral-100);
    stroke: var(--border);
}

.ia_movingAnalogIndicatorComponent__level1AlarmRange,
.ia_movingAnalogIndicatorComponent__level2AlarmRange {
    fill: var(--neutral-60);
    stroke: var(--border);
}

.ia_movingAnalogIndicatorComponent__level1AlarmRange--active {
    fill: var(--error);
    stroke: var(--border);
}

.ia_movingAnalogIndicatorComponent__level2AlarmRange--active {
    fill: var(--warningSecondary);
    stroke: var(--border);
}

.ia_movingAnalogIndicatorComponent__wedgeIndicator {
    fill: var(--neutral-60);
}

.ia_movingAnalogIndicatorComponent__setpointIndicator {
    fill: var(--neutral-10);
    stroke: var(--info);
}
