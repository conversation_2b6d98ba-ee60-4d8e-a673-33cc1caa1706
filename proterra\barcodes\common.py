from java.lang import Exception as JavaException
import system.db
import system.util
import traceback

_logger = system.util.getLogger('proterra.barcodes.common')


def validate(barcode):
    """
    Validate the structure of the barcode

    :param barcode: The barcode string to validate
    :type barcode: str
    :return: The validated barcode parts
    :rtype: list
    :raises ValueError: If the barcode is empty or invalid
    :raises ValueError: If the barcode contains non-ASCII printable characters
    """
    # Strip ASCII control characters from the barcode
    barcode = __strip_ascii_control_characters(barcode)
    # Validate that the barcode contains only ASCII printable characters
    if not __validate_ascii_printable_characters(barcode):
        raise ValueError('Barcode contains non-ASCII printable characters')
    # Check if the barcode is empty or None
    if not barcode:
        raise ValueError('Barcode is empty or invalid')
    # Split the barcode into parts
    barcode_splits = __split_barcode(barcode)
    # Check if the barcode has at least one part
    if not barcode_splits:
        raise ValueError('Barcode is empty or invalid')
    return barcode_splits


def __strip_ascii_control_characters(barcode):
    """
    Strip ASCII control characters from the barcode

    :param barcode: The barcode string to strip
    :type barcode: str
    :return: The stripped barcode string
    :rtype: str
    """
    return ''.join(char for char in barcode if 32 <= ord(char) <= 126)


def __validate_ascii_printable_characters(barcode):
    """
    Validate that the barcode contains only ASCII printable characters

    :param barcode: The barcode string to validate
    :type barcode: str
    :return: True if the barcode is valid, False otherwise
    :rtype: bool
    """
    return all(32 <= ord(char) <= 126 for char in barcode)


def __split_barcode(barcode):
    """
    Split the barcode into parts

    :param barcode: The barcode string to split
    :type barcode: str
    :return: A list of parts from the barcode
    :rtype: list
    """
    return [split for split in barcode.split('|') if split.strip()]


def get_search_string(barcode_splits):
    """
    Get the search string from the barcode parts

    :param barcode_splits: The parts of the barcode
    :type barcode_splits: list
    :return: The search string
    :rtype: str
    """
    search = '%' + '%'.join(barcode_splits) + '%'
    search = search.replace('/', '%')
    search = search.replace('_', '%')
    search = search.replace('.', '%')
    return search


def get_item_version_id(part, rev):
    """
    Get the item version id from the part and revision

    :param part: The part number
    :param rev: The revision number
    :return: The item version id, part number, and revision
    """
    ItemVersionId = -1
    ItemNo = ''
    ProductionVersion = ''

    # Define the query
    query = '''
        SELECT TOP(1)
            [ItemVersionId],
            [ItemNo],
            [ProductionVersion]
        FROM [product].[Item_Version]
        WHERE [ItemNo]=?
        AND [ProductionVersion] LIKE ?
    '''
    # Define the arguments
    args = [part, rev.replace('.', '%').replace('0', '%')]
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
        # Get the results
        if results:
            ItemVersionId = int(results[0].get('ItemVersionId', -1))
            ItemNo = str(results[0].get('ItemNo', ''))
            ProductionVersion = str(results[0].get('ProductionVersion', ''))
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return ItemVersionId, ItemNo, ProductionVersion
