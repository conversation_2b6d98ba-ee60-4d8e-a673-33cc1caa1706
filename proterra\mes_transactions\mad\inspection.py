from proterra.mes_transactions import txn, util
import system.date
import system.util


def dispenseResult(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        serialNumber = ''
        processStatus = True
        testResults = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "Barcode" in txnData["PLCData"].keys() and "Status" in txnData["PLCData"].keys():
                serialNumber = util.parseBarcode_Last(txnData["PLCData"]["Barcode"])
                processStatus = txnData["PLCData"]["Status"]
                testResults = str(system.util.jsonDecode(str(txnData["PLCData"]))["Result"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/DispenseSerialNumber"], [serialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_MAD_Inspection_DispenseResult]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SerialNumber", system.db.NVARCHAR, serialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_TestResults", system.db.NVARCHAR, testResults)
            call.registerInParam("p_ProcessStatus", system.db.BIT, processStatus)
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def cureReport(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        spineSerialNumber = ''
        processStatus = True
        testResults = ''
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/PathDATA', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "Barcode" in txnData["PLCData"].keys():
                spineSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["Barcode"])
                #testResults = str(system.util.jsonDecode(str(txnData["PLCData"]))["Result"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/SpineSerialNumber"], [spineSerialNumber])

                # Cure Report Data
                dataBrowse = system.tag.browse(txnData["PathDATA"])
                resultDict = {}
                for tag in dataBrowse:
                    tagValue = tag['value'].value
                    tagName = str(tag['name'])
                    if tag["hasChildren"]:
                        childrenDict = {}
                        tagName = str(tag['name'])
                        childrenDataBrowse = system.tag.browse(tag["fullPath"])
                        for child in childrenDataBrowse:
                            childrenDict[str(child['name'])] = child['value'].value
                        tagValue = childrenDict
                    resultDict[tagName] = tagValue

                # Add handling for Cure Report times - PCM 20221215
                timeDispense = resultDict["TimeDispense"]
                timeCure = resultDict["TimeCure"]

                timeDispMillis = processTimeDict(timeDispense)
                timeCureMillis = processTimeDict(timeCure)

                if timeDispMillis < 0 or timeCureMillis < 0:
                    raise Exception("Invalid time value")
                cureTime = formattedDateDiff(timeDispMillis, timeCureMillis)
                if cureTime.get('min', -1) != -1:
                    testResults = {
                        'CureTime': cureTime.get('min', ''),
                    }
            else:
                raise Exception("Values missing in PLCData")
        except:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_MAD_Inspection_CureReport]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SpineSerialNumber", system.db.NVARCHAR, spineSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_TestResults", system.db.NVARCHAR, testResults)
            call.registerInParam("p_ProcessStatus", system.db.BIT, processStatus)
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def processTimeDict(timeDict):
    '''
    '''
    yr = timeDict.get('Yr', None)
    mo = timeDict.get('Mo', None)
    day = timeDict.get('Da', None)
    hr = timeDict.get('Hr', None)
    min_ = timeDict.get('Min', None)
    sec = timeDict.get('Sec', None)
    usec = timeDict.get('uSec', None)

    date = system.date.getDate(yr, mo, day)
    date = system.date.addHours(system.date.addMinutes(system.date.addSeconds(date, sec), min_), hr)

    return system.date.toMillis(date)


def formattedDateDiff(millis1, millis2):
    '''
    '''
    milliDiff = abs(millis1 - millis2)
    minDiff = milliDiff / 1000 / 60.0

    return {'millis': milliDiff, 'min': minDiff}
