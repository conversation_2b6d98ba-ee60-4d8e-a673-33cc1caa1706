from abc import ABCMeta, abstractmethod
from datetime import datetime
from java.lang import Exception as JavaException
import system.db
import system.util
import traceback


class TransactionBase:
    __metaclass__ = ABCMeta

    _logger = system.util.getLogger('proterra.transactions.TransactionBase')
    _facility = None
    _area = None
    _line = -1
    _zone = -1
    _number = -1
    _tag_path_base = None
    _asset_id = -1
    _user = None

    def __init__(self, logger, facility, area, line, zone, number, tag_path_base, user):
        self._logger = logger
        self._facility = facility
        self._area = area
        self._line = line
        self._zone = zone
        self._number = number
        self._tag_path_base = tag_path_base
        self._user = user

    @property
    def facility(self):
        return self._facility

    @property
    def area(self):
        return self._area

    @property
    def line(self):
        return self._line

    @property
    def zone(self):
        return self._zone

    @property
    def number(self):
        return self._number

    @property
    def tag_path_base(self):
        return self._tag_path_base

    @property
    def asset_id(self):
        return self._asset_id

    @asset_id.setter
    def asset_id(self, value):
        self._asset_id = value

    @property
    def user(self):
        return self._user

    def execute(self):
        """
        Execute the transaction

        :return: A tuple containing a boolean indicating success and any error output of the transaction
        :rtype: tuple(bool, str)
        """
        start = datetime.now()
        success = False
        output = None
        try:
            self._execute()
            success = True
        except (Exception, JavaException):
            success = False
            output = traceback.format_exc()
            self._logger.error(output)
        finally:
            end = datetime.now()
            duration = (end - start).total_seconds() * 1000
            self.__log_transaction(success, output, duration)
        return success, output

    @abstractmethod
    def _execute(self):
        """
        Execute the transaction

        This method is called by the execute method and should contain the main logic for the transaction.
        This method should be implemented by subclasses to define the specific transaction logic.
        :raises NotImplementedError: If the method is not implemented in the subclass
        """
        raise NotImplementedError("Subclasses must implement this method.")

    def __log_transaction(self, success, output, duration):
        """
        Log the transaction in the database

        :param success: Whether the transaction was successful
        :type success: bool
        :param output: The output of the transaction, if any
        :type output: str
        :param duration: The duration of the transaction in milliseconds
        :type duration: float
        """
        data = {
            'facility': self.facility,
            'area': self.area,
            'line': self.line,
            'zone': self.zone,
            'number': self.number,
            'tag_path_base': self.tag_path_base,
            'asset_id': self.asset_id,
            'work_order_id': self.work_order_id,
            'serial': self.serial,
            'serial_id': self.serial_id,
            'vendor_serial': self.vendor_serial,
            'vendor_serial_id': self.vendor_serial_id,
            'user': self.user
        }

        # Define the query
        query = '''
            INSERT INTO [mes].[TXN_Log] (
                [ProcessStep],
                [TagPath],
                [Success],
                [TxnOutput],
                [DataPayload],
                [TxnTimestamp],
                [Duration_ms],
                [Line],
                [TxnSerialNumber]
            )
            VALUES (
                ?,
                ?,
                ?,
                ?,
                ?,
                GETDATE(),
                ?,
                ?,
                ?,
                ?
            )
        '''
        # Define the arguments
        args = [
            self.number,
            self.tag_path_base,
            success,
            output,
            data,
            duration,
            self.line,
            self.serial]
        try:
            # Run the query
            system.db.runPrepUpdate(query, args, 'REVMES')
        except (Exception, JavaException):
            self._logger.error(traceback.format_exc())


def execute_transaction(facility, area, line, number):
    """
    Execute a transaction based on the provided parameters

    :param facility: The facility where the transaction is executed
    :type facility: str
    :param area: The area within the facility
    :type area: str
    :param line: The line number for the transaction
    :type line: int
    :param number: The transaction number
    :type number: int
    :return: A tuple containing a boolean indicating success and any error output of the transaction
    :rtype: tuple(bool, str)
    """
    tx_class = None
    if facility == 'GVB':
        if area == 'Module':
            if line == 1:
                from proterra.transactions.gvb.module.line_1 import (
                    txn_11115,
                    txn_11165
                )
                if number == 11115:
                    tx_class = txn_11115.T11115
                elif number == 11165:
                    tx_class = txn_11165.T11165
            elif line == 2:
                from proterra.transactions.gvb.module.line_2 import (
                    txn_11115,
                    txn_11165
                )
                if number == 11115:
                    tx_class = txn_11115.T11115
                elif number == 11165:
                    tx_class = txn_11165.T11165
    # Check if the transaction class was found
    if not tx_class:
        raise ValueError('Transaction for {} {} Line {} #{} not found'.format(facility, area, line, number))
    # Create an instance of the transaction class
    tx = tx_class()
    return tx.execute()
