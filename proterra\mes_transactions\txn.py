from java.lang import Exception as JavaException
import traceback
import system.date
import system.util


# For Handshake_v3 transactions

SPROC_RESULTS = {
    'Success': False, 'ResultCode': 0, 'ResultMessage': 'No SPROC Execution', 'RespSeries': 0, 'RespType': 0, 'RespStatus': 0, 'SPDuration': 0, 'TxnSerialNumber': ''
}


def writeResponse(pathRESP, sprocResults):
    try:
        if sprocResults["Success"]:
            system.tag.writeBlocking(
                [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status'],
                [sprocResults['RespSeries'], sprocResults['RespType'], sprocResults['RespStatus']]
            )
        else:
            system.tag.writeBlocking(
                [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status'],
                [sprocResults['RespSeries'], sprocResults['RespType'], sprocResults['ResultCode']]
            )
    except:
        pass


def writeResponseV2(pathRESP, sprocResults):
    try:
        if sprocResults["Success"]:
            paths = [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status']
            values = [sprocResults['RespSeries'], sprocResults['RespType'], sprocResults['RespStatus']]
            writes = system.tag.writeBlocking(paths, values)
        else:
            paths = [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status']
            values = [sprocResults['RespSeries'], sprocResults['RespType'], sprocResults['ResultCode']]
            writes = system.tag.writeBlocking(paths, values)
    except:
        exc = traceback.format_exc()
        DataAccess.Log.LogError(message=exc, functionName="writeResponse", application="proterra.mes_transactions.txn")
    else:
        badWriteInfo = [str(p)+':'+str(w.quality) for p, w in zip(paths, writes) if not w.isGood()]
        if len(badWriteInfo) > 0:
            DataAccess.Log.LogError(message='Failed to Write: '+', '.join(badWriteInfo),
                                    functionName="writeResponse", application="proterra.mes_transactions.txn")

# For Handshake_v3 transactions


def determineRSD(success, message, code, pathRSD, baseTagPath):
    rsdValue = 2
    failed = 1
    complete = 0

    if success:
        rsdValue = 1
        failed = 0
        message = ''
        code = -1
        complete = 1

    paths = [baseTagPath + '/Status/Trigger', baseTagPath + '/Status/Failed', baseTagPath + '/Status/FailureMessage',
             baseTagPath + '/Status/FailureCode', pathRSD, baseTagPath + '/Status/Complete']

    #Trigger, Failed, FailMsg, FailCode, RSD, Complete
    vals = [0, failed, message, code, rsdValue, complete]
    system.tag.writeBlocking(paths, vals)

# For CRABB transactions


def determineRSD_CRABB(success, message, code, baseTagPath):
    failed = 1
    complete = 0

    if success:
        failed = 0
        message = ''
        code = -1
        complete = 1

    paths = [baseTagPath + '/Status/Failed', baseTagPath + '/Status/FailureMessage',
             baseTagPath + '/Status/FailureCode', baseTagPath + '/Status/Complete', baseTagPath + '/Status/InProcess']
    #Failed, FailMsg, FailCode, Complete, InProcess
    vals = [failed, message, code, complete, 0]
    system.tag.writeBlocking(paths, vals)


# For Handshake_v3 transactions
def handleUncaughtExceptions(ex, baseTagPath):
    print(repr(ex))
    DataAccess.Log.LogError(repr(ex), functionName=baseTagPath, application='transactionTriggerLogic', user='MES')
    system.tag.writeBlocking([
        baseTagPath+"/Status/Complete",
        baseTagPath+"/Status/Failed",
        baseTagPath+"/Status/FailureMessage",
        baseTagPath+"/Status/FailureCode",
        baseTagPath+"/Status/InProcess",
        baseTagPath+'/Status/Trigger'],

        [0, 1, repr(ex),  9999, False, False])


def getTXNInputValues(txnInputs, baseTagPath):
    # Looping to get the tag paths and read tags
    paths = []
    for inpt in txnInputs:
        paths.append(baseTagPath + inpt[1])
    tagsRead = system.tag.readBlocking(paths)

    # Looping to create the dictionary with the read values
    txnInputVal = {}
    inputCount = len(txnInputs)
    n = 0
    while (n < inputCount):
        txnInputVal[txnInputs[n][0]] = tagsRead[n].value
        n = n + 1
    return txnInputVal


def getTXNInputValuesV2(txnInputs, baseTagPath):
    tagsRead = system.tag.readBlocking([baseTagPath+i[1] for i in txnInputs])
    txnInputVal = {txnInputs[e][0]: tr.value for e, tr in enumerate(tagsRead)}
    return txnInputVal


# Looks one level up on the baseTagPath for the Authentication UDT to get the UserName
def getTXNUserName(baseTagPath):
    # Pull username from authentication tag
    try:
        basePath = baseTagPath.rsplit("/", 1)[0]
        userNamePath = basePath+'/Authentication/UserName'
        user = system.tag.readBlocking(userNamePath)[0].value
    except:
        user = 'No User Found'
    return user


def logEntry(txnNum, baseTagPath, txnOutput, dataPayload, txnStart):
    try:
        duration = system.date.toMillis(system.date.now()) - txnStart
        sproc = """
			EXECUTE [REVMES].[mes].[sp_TXN_LogEntry]
		       @p_TxnNum = ?
		      ,@p_TagPath = ?
		      ,@p_Success = ?
		      ,@p_FailureCode = ?
		      ,@p_TxnOutput = ?
		      ,@p_DataPayload = ?
		      ,@p_Duration_ms = ?
		      ,@p_SPDuration_ms = ?
		"""
        params = [
            txnNum or 0, baseTagPath, txnOutput['Success'], txnOutput['ResultCode'], str(
                txnOutput), str(dataPayload), duration, txnOutput['SPDuration']
        ]
        system.db.runPrepUpdate(sproc, params, 'MESDB')
    except Exception as ex:
        print(repr(ex))
        pass


# New Features
def getTXNData(txnInputs, baseTagPath):
    tagsRead = system.tag.readBlocking([baseTagPath+i for i in txnInputs])
    txnData = {str(txnInputs[e]).split('/')[-1]: tr.value for e, tr in enumerate(tagsRead)}
    return txnData


def runSproc(call):
    try:
        sprocResults = dict(SPROC_RESULTS)
        # Sproc Execution
        system.db.execSProcCall(call)
        results = system.dataset.toPyDataSet(call.getResultSet())[0]
        sprocResults['Success'] = results['Result'] == 'Success'
        sprocResults['ResultCode'] = results['ResultCode']
        sprocResults['ResultMessage'] = results['ResultMessage']
        sprocResults['RespSeries'] = results['RespSeries']
        sprocResults['RespType'] = results['RespType']
        sprocResults['RespStatus'] = results['RespStatus']
        sprocResults['SPDuration'] = results['SPDuration']
        sprocResults['TxnSerialNumber'] = results['TxnSerialNumber']
        return sprocResults
    except JavaException as e:
        raise Exception("Error with DB Stored Procedure. Check parameters.")


def performTXN(baseTagPath, txn_start, readError, txnData, call):
    sprocResults = dict(SPROC_RESULTS)
    #######################
    # SPROC
    if not readError:
        sprocResults = dict(runSproc(call))
    else:
        sprocResults['ResultCode'] = 1015
        sprocResults['ResultMessage'] = "Values missing in PLC Data or Null Inputs"

    pathALARM = system.tag.readBlocking(baseTagPath+"/Inputs/PathALARM")[0].value
    alarm = system.tag.readBlocking(pathALARM)[0].value

    if alarm != 102:
        #######################
        # RESPONSE
        writeResponse(txnData['PathRESP'], sprocResults)
        #######################
        # RSD
        determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                     sprocResults['ResultCode'], txnData['PathRSD'], baseTagPath)
    #######################
    # TXN END
    exclude_keys = {'PathRSD', 'PathRESP', 'PathDATA'}
    dataPayload = {k: txnData[k] for k in set(list(txnData.keys())) - exclude_keys}
    txnLogEntry(txnData['ProcessStep'], baseTagPath, sprocResults, dataPayload, txn_start)


def performTXN_CRABB(baseTagPath, txn_start, readError, txnData, call):
    sprocResults = dict(SPROC_RESULTS)
    #######################
    # SPROC
    if not readError:
        sprocResults = dict(runSproc(call))
    else:
        sprocResults['ResultCode'] = 1015
        sprocResults['ResultMessage'] = "Values missing in PLC Data or Null Inputs"

    #######################
    # RSD
    determineRSD_CRABB(sprocResults['Success'], sprocResults['ResultMessage'],
                       sprocResults['ResultCode'], baseTagPath)
    #######################
    # TXN END
    exclude_keys = {'PathPLC'}
    dataPayload = {k: txnData[k] for k in set(list(txnData.keys())) - exclude_keys}
    txnLogEntry(txnData['ProcessStep'], baseTagPath, sprocResults, dataPayload, txn_start)


def performTXN_NORESP(baseTagPath, txn_start, readError, txnData, call):
    sprocResults = dict(SPROC_RESULTS)
    #######################
    # SPROC
    if not readError:
        sprocResults = dict(runSproc(call))
    else:
        sprocResults['ResultCode'] = 1015
        sprocResults['ResultMessage'] = "Values missing in PLC Data or Null Inputs"
    #######################
    # TXN END
    exclude_keys = {'PathRSD', 'PathRESP'}
    dataPayload = {k: txnData[k] for k in set(list(txnData.keys())) - exclude_keys}
    txnLogEntry(txnData['ProcessStep'], baseTagPath, sprocResults, dataPayload, txn_start)


def txnLogEntry(txnNum, baseTagPath, txnOutput, dataPayload, txnStart):
    try:
        duration = system.date.toMillis(system.date.now()) - txnStart
        line = None
        try:
            line = int(baseTagPath.split("Line", 1)[1][0])
        except ValueError:
            pass

        sproc = """
			EXECUTE [REVMES].[mes].[sp_TXN_LogEntry]
		       @p_TxnNum = ?
		      ,@p_TagPath = ?
		      ,@p_Success = ?
		      ,@p_FailureCode = ?
		      ,@p_TxnOutput = ?
		      ,@p_DataPayload = ?
		      ,@p_Duration_ms = ?
		      ,@p_SPDuration_ms = ?
		      ,@p_Line = ?
		      ,@p_TxnSerialNumber = ?
		"""
        params = [
            txnNum or 0, baseTagPath, txnOutput['Success'], txnOutput['ResultCode'], str(txnOutput), str(
                dataPayload), duration, txnOutput['SPDuration'], line, txnOutput['TxnSerialNumber']
        ]
        system.db.runPrepUpdate(sproc, params, 'MESDB')
    except Exception as ex:
        print(repr(ex))
        pass
