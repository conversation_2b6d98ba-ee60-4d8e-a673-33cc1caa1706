from java.lang import Exception as JavaException
import system.db
import system.util
import traceback
from . import common as common_barcodes

_logger = system.util.getLogger('proterra.barcodes.vendor')


def validate_cassette_sleeve_barcode(barcode):
    """
    Validate a cassette sleeve barcode and return its serial number, part number, revision, item version id, and search string

    :param barcode: The barcode string
    :type barcode: str
    :returns: A tuple containing the serial number, part number, revision, item version id, and search string
    :rtype: tuple
    :raises ValueError: If the barcode is invalid or item version not found
    :raises ValueError: If the barcode does not match the expected format
    :raises ValueError: If the barcode contains non-ASCII printable characters or is empty
    """
    serial = ''
    part = ''
    rev = ''
    item_version_id = -1

    # Validate the common structure of the barcode
    barcode_splits = common_barcodes.validate(barcode)

    # Check if the barcode has 8 parts (new format) or 1 part (old format)
    if len(barcode_splits) == 8:
        # The barcode is in the new format with 8 parts
        # i.e. 1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01
        serial = ''.join(barcode_splits)  # 1599194-2464D01PA/PPEGF10202410281300001108101
        part = barcode_splits[1]  # 194-2464
        rev = barcode_splits[2]  # D01
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    elif len(barcode_splits) == 1:
        # The barcode is in the old format with 1 part
        # i.e. 1599052550C0212312023K23L0640001300149347
        serial = ''.join(barcode_splits)  # 1599052550C0212312023K23L0640001300149347
        part = serial[4:10]  # 052550
        rev = serial[10:13]  # C02
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    else:
        # The barcode is invalid
        raise ValueError('Invalid cassette sleeve barcode format')

    # Check if the item version id was found
    if item_version_id == -1 or not part or not rev:
        raise ValueError('Item version not found for part: {part}, revision: {rev}'.format(part=part, rev=rev))

    # Store the serial barcode
    __store_raw_barcode(serial, barcode)

    # Return the serial number, part number, revision, item version id, and search string
    return serial, part, rev, item_version_id, common_barcodes.get_search_string(barcode_splits)


def validate_foil_barcode(barcode):
    """
    Validate a foil barcode and return its serial number, part number, revision, item version id, and search string

    :param barcode: The barcode string
    :type barcode: str
    :returns: A tuple containing the serial number, part number, revision, item version id, and search string
    :rtype: tuple
    :raises ValueError: If the barcode is invalid or item version not found
    :raises ValueError: If the barcode does not match the expected format
    :raises ValueError: If the barcode contains non-ASCII printable characters or is empty
    """
    serial = ''
    part = ''
    rev = ''
    item_version_id = -1

    # Validate the common structure of the barcode
    barcode_splits = common_barcodes.validate(barcode)

    # Check if the barcode has 6 parts (new format) or 5 part (old format)
    if len(barcode_splits) == 6:
        # The barcode is in the new format with 6 parts
        # i.e. 3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551
        serial = ''.join(barcode_splits)  # 3661133-0447C.1241028K024A210010551
        part = barcode_splits[1]  # 133-0447
        rev = barcode_splits[2]  # C.1
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    elif len(barcode_splits) == 5:
        # The barcode is in the old format with 5 parts
        # i.e. 3661 | 133-0468 | C.1 | 102723 | K023A23001 1432
        serial = ''.join(barcode_splits)  # 3661133-0468C.1102723K023A230011432
        part = barcode_splits[1]  # 133-0468
        rev = barcode_splits[2]  # C.1
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    else:
        # The barcode is invalid
        raise ValueError('Invalid foil barcode format')

    # Check if the item version id was found
    if item_version_id == -1 or not part or not rev:
        raise ValueError('Item version not found for part: {part}, revision: {rev}'.format(part=part, rev=rev))

    # Store the serial barcode
    __store_raw_barcode(serial, barcode)

    # Return the serial number, part number, revision, item version id, and search string
    return serial, part, rev, item_version_id, common_barcodes.get_search_string(barcode_splits)


def validate_spine_barcode(barcode):
    """
    Validate a spine barcode and return its serial number, part number, revision, item version id, and search string

    :param barcode: The barcode string
    :type barcode: str
    :returns: A tuple containing the serial number, part number, revision, item version id, and search string
    :rtype: tuple
    :raises ValueError: If the barcode is invalid or item version not found
    :raises ValueError: If the barcode does not match the expected format
    :raises ValueError: If the barcode contains non-ASCII printable characters or is empty
    """
    serial = ''
    part = ''
    rev = ''
    item_version_id = -1

    # Validate the common structure of the barcode
    barcode_splits = common_barcodes.validate(barcode)

    # Check if the barcode has 8 parts (new format) or 1 part (old format)
    if len(barcode_splits) == 6:
        # The barcode is in the new format with 6 parts
        # i.e. 4222 | 195-3560 | A.1 | JT | 08302024 | 0022
        serial = ''.join(barcode_splits)  # 4222195-3560A.1JT083020240022
        part = barcode_splits[1]  # 195-3560
        rev = barcode_splits[2]  # A.1
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    elif len(barcode_splits) == 1:
        # The barcode is in the old format with 1 part
        # i.e. 4222187-5071A.1061020230285
        serial = ''.join(barcode_splits)  # 4222187-5071A.1061020230285
        part = serial[4:12]  # 187-5071
        rev = serial[12:15]  # A.1
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    else:
        # The barcode is invalid
        raise ValueError('Invalid spine barcode format')

    # Check if the item version id was found
    if item_version_id == -1 or not part or not rev:
        raise ValueError('Item version not found for part: {part}, revision: {rev}'.format(part=part, rev=rev))

    # Store the serial barcode
    __store_raw_barcode(serial, barcode)

    # Return the serial number, part number, revision, item version id, and search string
    return serial, part, rev, item_version_id, common_barcodes.get_search_string(barcode_splits)


def __store_raw_barcode(serial, barcode):
    """
    Store the raw barcode in the database

    :param serial: The serial number to store
    :param barcode: The full barcode string
    """
    """
    Store a link between a serial and a barcode

    :param serial: The serial string
    :param barcode: The barcode string
    :param tx: An optional transaction
    """
    # Make sure we have something to store
    if not serial or not barcode:
        return
    # Define the query
    query = '''
        INSERT INTO [mes].[RawBarcodes] (
            [SerialNoNumber],
            [RawBarcode]
        )
        VALUES (
            ?,
            ?
        )
    '''
    # Define the arguments
    args = [serial, barcode]
    try:
        # Run the query
        system.db.runPrepUpdate(query, args, 'REVMES')
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
