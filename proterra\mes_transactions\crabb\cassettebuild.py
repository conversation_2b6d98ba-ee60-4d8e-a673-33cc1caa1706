from java.lang import Exception as JavaException
from proterra.models import barcode
from proterra.mes_transactions import txn, util
import system.date
import system.util


def validateCellType(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/ProcessStep', '/LotNo', '/CellboxBarcode', '/ModelCode', '/Quantity'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_CRABB_CB_ValidateCellType]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_LotNo", system.db.NVARCHAR, txnData['LotNo'])
            call.registerInParam("p_Barcode", system.db.NVARCHAR, txnData['CellboxBarcode'])
            call.registerInParam("p_ModelCode", system.db.NVARCHAR, txnData['ModelCode'])
            call.registerInParam("p_Quantity", system.db.INTEGER, txnData['Quantity'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN_CRABB(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        system.tag.writeBlocking([baseTagPath+"/Status/_MES_Log"], ['Error'+repr(ex)])


def cellboxTest(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/ProcessStep', '/CellboxBarcode', '/CellboxLot', '/FailCount', '/PassCount'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_CRABB_CB_CellboxTest]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
            call.registerInParam("p_CellboxBarcode", system.db.NVARCHAR, txnData['CellboxBarcode'])
            call.registerInParam("p_CellboxLot", system.db.NVARCHAR, txnData['CellboxLot'])
            call.registerInParam("p_FailCount", system.db.INTEGER, txnData['FailCount'])
            call.registerInParam("p_PassCount", system.db.INTEGER, txnData['PassCount'])

        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN_CRABB(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        system.tag.writeBlocking([baseTagPath+"/Status/_MES_Log"], ['Error'+repr(ex)])
        pass


def validateSerialNumber(baseTagPath):
    try:
        # Function Variables
        fnName = "validateSerialNumber"
        fnlogger = system.util.getLogger(fnName)
        txn_start = system.date.toMillis(system.date.now())
        params = []
        readError = False

        # MES Response
        sprocResults = {'Success': False, 'ResultCode': 0, 'ResultMessage': '', 'SerialNumber': ''}

        ###################################
        ######### DATA COLLECTION #########
        try:
            txnInputValues = {}
            # Reading the parameters and inputs
            # This is a tuple, in order to maintain the correct order
            txnInputs = [
                ('WorkOrderId', '/WorkOrderId'),
                ('AssetId', '/Inputs/AssetId'),
                ('CRABB_PLC', '/Parameters/rootTagPath'),
            ]
            txnInputValues = txn.getTXNInputValues(txnInputs, baseTagPath)
            userName = txn.getTXNUserName(baseTagPath)
            snProterra = system.tag.readBlocking(txnInputValues["CRABB_PLC"]+'/RobotInHand_SN_Proterra')[0].value or '0'
            snManufacturer = system.tag.readBlocking(
                txnInputValues["CRABB_PLC"]+'/RobotInHand_SN_Manufacturer')[0].value or '0'
            snManufacturer = barcode.get_cassette_serial(snManufacturer)
        except Exception as e:
            readError = True
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = 'MES did not receive values in the expected DATA tag fields'
            fnlogger.info(sprocResults['ResultMessage'] + ' ' + repr(e))

        #########################
        ######### SPROC #########
        if not readError:
            sproc = """
				EXECUTE [REVMES].[txn].[sp_CRABB_CB_ValidateSerialNumber]
			      @p_WorkOrderId = ?
			      ,@p_AssetId = ?
			      ,@p_Username = ?
			      ,@p_ProterraSN = ?
			      ,@p_ManufacturerSN = ?
			"""
            params = [
                txnInputValues['WorkOrderId'],
                txnInputValues['AssetId'],
                userName,
                snProterra,
                snManufacturer
            ]
            try:
                # Sproc Execution
                results = util.convertResultToJSON(system.db.runPrepQuery(sproc, params, 'REVMES'))
                sprocResults['Success'] = results['Result'] == 'Success'
                sprocResults['ResultCode'] = results['ResultCode']
                sprocResults['ResultMessage'] = results['ResultMessage']
                sprocResults['SerialNumber'] = results['SerialNumber']
            except JavaException as e:
                # fnlogger.info(repr(e))
                raise Exception(repr(e))

        ##########################
        ######## RESPONSE ########
        # Writes the generated serial number to the PLC
        if sprocResults['SerialNumber'] != '':
            system.tag.writeBlocking([baseTagPath+"/SerialNumber"], [sprocResults['SerialNumber']])

        #########################
        ######## TXN END ########
        txn.logEntry(11115, baseTagPath, sprocResults, params, txn_start)
    # For any other exception not handled correctly
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)
