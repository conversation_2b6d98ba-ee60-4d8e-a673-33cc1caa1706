from java.lang import Exception as JavaException
import system.db
import system.util
import traceback

_logger = system.util.getLogger('proterra.transactions.work_order')


class WorkOrder:
    _id = -1
    _number = ''
    _item_version_id = -1
    _prefix = ''
    _total_count = 0
    _used_count = 0
    _next_work_order_id = -1

    def __init__(self, work_order_id):
        """
        Initialize the WorkOrder object with the given work order ID

        :param work_order_id: The ID of the work order
        :type work_order_id: int
        """
        # Define the query
        query = '''
            SELECT
                [wo].[WorkOrderId],
                [wo].[WorkOrderNumber],
                [wo].[ItemVersionId],
                (SELECT ia.AttrValue
                    FROM product.Item_Attr ia
                    JOIN attr.Attr a
                    ON ia.AttrId = a.AttrId
                    WHERE a.AttrName = 'Part Type Letter'
                    AND ia.ItemVersionId=wo.ItemVersionId) AS Prefix,
                wo.Quantity AS TotalCount,
                (SELECT COUNT(*) AS Count
                    FROM mes.Work_Order_Bom_Link wobl
                    LEFT JOIN mes.Work_Order_Bom_Item wobi
                    ON wobl.WorkOrderBOMLinkId=wobi.WorkOrderBomLinkId
                    WHERE wobl.WorkOrderId=wo.WorkOrderId
                    AND wobi.SerialNo IS NOT NULL) AS UsedCount
            FROM mes.Work_Order wo
            WHERE wo.WorkOrderId=?
        '''
        # Define the arguments
        args = [work_order_id]
        try:
            # Run the query
            results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
            # Get the results
            if results:
                self._id = int(results[0].get('WorkOrderId', -1))
                self._number = str(results[0].get('WorkOrderNumber', ''))
                self._item_version_id = int(results[0].get('ItemVersionId', -1))
                self._prefix = str(results[0].get('Prefix', ''))
                self._total_count = int(results[0].get('TotalCount', 0))
                self._used_count = int(results[0].get('UsedCount', 0))
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())

    @property
    def id(self):
        return self._id

    @property
    def number(self):
        return self._number

    @property
    def item_version_id(self):
        return self._item_version_id

    @property
    def prefix(self):
        return self._prefix

    @property
    def total_count(self):
        return self._total_count

    @property
    def used_count(self):
        return self._used_count

    @property
    def available_count(self):
        return max(self._total_count - self._used_count if self._total_count > 0 else 0, 0)

    @property
    def next_work_order_id(self):
        if self._next_work_order_id == -1:
            # Define the query
            query = '''
                SELECT TOP(1) wo.WorkOrderId
                FROM mes.Work_Order wo
                WHERE wo.ItemVersionId = (
                    SELECT ItemVersionId
                    FROM mes.Work_Order
                    WHERE WorkOrderId=?
                )
                AND wo.WorkOrderStateId IN (1,2,3,7)
                AND wo.WorkOrderNumber LIKE ?
                AND wo.WorkOrderId!=?
                ORDER BY wo.PlannedStartDate,wo.WorkOrderNumber
            '''
            # Define the arguments
            args = [self._id, '%{}'.format(self._number[-2:]), self._id]
            try:
                # Run the query
                result = system.db.runScalarPrepQuery(query, args, 'MESDB_Replica')
                if result:
                    self._next_work_order_id = int(result)
            except (Exception, JavaException):
                _logger.error(traceback.format_exc())
        return self._next_work_order_id

    def get_valid_operation_item_version_ids(self, operation):
        """
        Get the valid operation step item version ids for the work order operation step

        :param operation: The work order operation step name
        :type operation: str
        :return: A list of valid operation item version ids
        :rtype: list
        """
        item_version_ids = []
        # Define the query
        query = '''
            SELECT DISTINCT wobi.ItemVersionId
            FROM mes.Work_Order wo
            LEFT JOIN mes.Work_Order_Bom_Link wobl
            ON wo.WorkOrderId=wobl.WorkOrderId
            LEFT JOIN mes.Work_Order_Operation_Step woos
            ON wobl.OperationStepId=woos.WorkOrderOperationStepId
            LEFT JOIN mes.Work_Order_Bom_Item wobi
            ON wobl.WorkOrderBomLinkId=wobi.WorkOrderBomLinkId
            WHERE wo.WorkOrderId=?
            AND woos.OperationStepName=?
            AND wo.ItemVersionId!=wobi.ItemVersionId
        '''
        # Define the arguments
        args = [self.id, operation]
        try:
            # Run the query
            results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
            for row in results:
                item_version_ids.append(int(row.get('ItemVersionId', -1)))
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())
        return item_version_ids
