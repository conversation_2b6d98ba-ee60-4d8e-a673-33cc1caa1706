import system.db
import system.date
import json
import logging
from datetime import datetime

# Configure logger
logger = logging.getLogger("ModuleQualityCheck")


def convert_to_est(utc_datetime):
    """Convert UTC datetime to EST string format"""
    if utc_datetime:
        try:
            est_datetime = system.date.toTimezone(utc_datetime, "America/New_York")
            return system.date.format(est_datetime, "yyyy-MM-dd HH:mm:ss") + " EST"
        except Exception as e:
            logger.warning("Date conversion failed: {}".format(str(e)))
            return str(utc_datetime) + " UTC"
    return None


def check_genealogy(module_serial, revmes_db="REVMES"):
    """
    Check module genealogy and validate required components

    Returns:
        dict: {
            'success': bool,
            'genealogy_valid': bool,
            'components': {'A_block': str/None, 'B_block': str/None, 'Spine': str/None},
            'all_components': [list of all component dicts],
            'missing_components': [list of missing component types],
            'error': str/None
        }
    """
    result = {
        "success": False,
        "genealogy_valid": False,
        "components": {"A_block": None, "B_block": None, "Spine": None},
        "all_components": [],
        "missing_components": [],
        "error": None,
    }

    try:
        genealogy_query = """
        SELECT ItemSerialNoId, SerialNumber, ItemLotId, LotNumber, ItemVersionId, 
               ItemNo, ItemRev, ItemDesc, ParentItemSerialNoId, ConsumedAt, 
               ItemConsumptionId, WorkOrderId, GEN_LVL
        FROM mes.fn_tbl_SerialNumberGenealogy(?)
        ORDER BY GEN_LVL, SerialNumber
        """

        genealogy_data = system.db.runPrepQuery(
            genealogy_query, [module_serial], revmes_db
        )

        if not genealogy_data:
            result["error"] = "No genealogy data found for module"
            logger.error("No genealogy data found for module: {}".format(module_serial))
            return result

        # Process genealogy data
        component_serials = {"A": [], "B": [], "S": []}

        for row in genealogy_data:
            component_data = {
                "serial": row["SerialNumber"],
                "item_no": row.get("ItemNo"),
                "item_rev": row.get("ItemRev"),
                "item_desc": row.get("ItemDesc"),
                "generation_level": row["GEN_LVL"],
                "consumed_at": convert_to_est(row.get("ConsumedAt")),
                "lot_number": row.get("LotNumber"),
            }
            result["all_components"].append(component_data)

            serial = row["SerialNumber"]
            if serial and len(serial) >= 12:
                prefix = serial[0].upper()
                if prefix in component_serials:
                    component_serials[prefix].append(serial)

        # Validate required components
        result["components"]["A_block"] = (
            component_serials["A"][0] if component_serials["A"] else None
        )
        result["components"]["B_block"] = (
            component_serials["B"][0] if component_serials["B"] else None
        )
        result["components"]["Spine"] = (
            component_serials["S"][0] if component_serials["S"] else None
        )

        # Check for missing components
        if not result["components"]["A_block"]:
            result["missing_components"].append("A-Block")
        if not result["components"]["B_block"]:
            result["missing_components"].append("B-Block")
        if not result["components"]["Spine"]:
            result["missing_components"].append("Spine")

        result["genealogy_valid"] = len(result["missing_components"]) == 0
        result["success"] = True

    except Exception as e:
        result["error"] = "Error checking genealogy: {}".format(str(e))
        logger.error(
            "Error checking genealogy for {}: {}".format(module_serial, str(e))
        )

    return result


def check_ncf_status(all_serials, revmes_db="REVMES"):
    """
    Check NCF status for all provided serial numbers

    Args:
        all_serials: list of serial numbers to check

    Returns:
        dict: {
            'success': bool,
            'has_active_ncf': bool,
            'ncf_details': [list of NCF detail dicts],
            'active_ncf_count': int,
            'error': str/None
        }
    """
    result = {
        "success": False,
        "has_active_ncf": False,
        "ncf_details": [],
        "active_ncf_count": 0,
        "error": None,
    }

    try:
        for serial in all_serials:
            ncf_query = """
            SELECT TOP 1 ncfId, ncfStateName, Complete, SerialNumber, 
                   CreatedAt_EST, InitialNotes, Notes
            FROM mes.vw_NCF_Info 
            WHERE SerialNumber = ?
            ORDER BY CreatedAt_EST DESC
            """

            ncf_data = system.db.runPrepQuery(ncf_query, [serial], revmes_db)

            if ncf_data:
                ncf_row = ncf_data[0]
                is_active = ncf_row["Complete"] != 1

                ncf_info = {
                    "serial": serial,
                    "ncf_id": ncf_row["ncfId"],
                    "state": ncf_row["ncfStateName"],
                    "is_active": is_active,
                    "created_at_est": str(ncf_row["CreatedAt_EST"]),
                    "notes": ncf_row.get("Notes") or ncf_row.get("InitialNotes"),
                }

                result["ncf_details"].append(ncf_info)

                if is_active:
                    result["has_active_ncf"] = True
                    result["active_ncf_count"] += 1

        result["success"] = True

    except Exception as e:
        result["error"] = "Error checking NCF status: {}".format(str(e))
        logger.error("Error checking NCF status: {}".format(str(e)))

    return result


def check_mad_dispense_results(all_serials, reporting_db="REPORTINGDB"):
    """
    Check MAD Dispense test results for all serials

    Returns:
        dict: {
            'success': bool,
            'test_results': [list of test result dicts],
            'total_tests': int,
            'failed_tests': int,
            'error': str/None
        }
    """
    result = {
        "success": False,
        "test_results": [],
        "total_tests": 0,
        "failed_tests": 0,
        "error": None,
    }

    try:
        for serial in all_serials:
            dispense_query = """
            SELECT SerialNumber, AssetName, EntryCreatedAt, Passed, 
                   MaterialA_Volume, MaterialB_Volume, TotalVolume, CycleRatio,
                   Max_CycleRatio, Min_CycleRatio
            FROM TestResults_MAD_DispenseResult 
            WHERE SerialNumber = ?
            ORDER BY EntryCreatedAt DESC
            """

            dispense_data = system.db.runPrepQuery(
                dispense_query, [serial], reporting_db
            )

            for row in dispense_data:
                test_result = {
                    "serial": serial,
                    "asset_name": row["AssetName"],
                    "passed": bool(row["Passed"]),
                    "test_time_est": convert_to_est(row["EntryCreatedAt"]),
                    "material_a_volume": row.get("MaterialA_Volume"),
                    "material_b_volume": row.get("MaterialB_Volume"),
                    "total_volume": row.get("TotalVolume"),
                    "cycle_ratio": row.get("CycleRatio"),
                    "max_cycle_ratio": row.get("Max_CycleRatio"),
                    "min_cycle_ratio": row.get("Min_CycleRatio"),
                }

                result["test_results"].append(test_result)
                result["total_tests"] += 1

                if not test_result["passed"]:
                    result["failed_tests"] += 1

        result["success"] = True

    except Exception as e:
        result["error"] = "Error checking MAD Dispense results: {}".format(str(e))
        logger.error("Error checking MAD Dispense results: {}".format(str(e)))

    return result


def check_mad_cure_results(module_serial, reporting_db="REPORTINGDB"):
    """
    Check MAD Cure test results for module

    Returns:
        dict: {
            'success': bool,
            'test_results': [list of test result dicts],
            'total_tests': int,
            'failed_tests': int,
            'error': str/None
        }
    """
    result = {
        "success": False,
        "test_results": [],
        "total_tests": 0,
        "failed_tests": 0,
        "error": None,
    }

    try:
        cure_query = """
        SELECT SerialNumber, AssetName, EntryCreatedAt, Passed, CureTime, CureLocation
        FROM TestResults_MAD_CureReport 
        WHERE SerialNumber = ?
        ORDER BY EntryCreatedAt DESC
        """

        cure_data = system.db.runPrepQuery(cure_query, [module_serial], reporting_db)

        for row in cure_data:
            test_result = {
                "serial": module_serial,
                "asset_name": row["AssetName"],
                "passed": bool(row["Passed"]),
                "test_time_est": convert_to_est(row["EntryCreatedAt"]),
                "cure_time": row.get("CureTime"),
                "cure_location": row.get("CureLocation"),
            }

            result["test_results"].append(test_result)
            result["total_tests"] += 1

            if not test_result["passed"]:
                result["failed_tests"] += 1

        result["success"] = True

    except Exception as e:
        result["error"] = "Error checking MAD Cure results: {}".format(str(e))
        logger.error("Error checking MAD Cure results: {}".format(str(e)))

    return result


def check_full_meol_results(module_serial, reporting_db="REPORTINGDB"):
    """
    Check Full MEOL test results for module

    Returns:
        dict: {
            'success': bool,
            'test_results': [list of test result dicts],
            'total_tests': int,
            'failed_tests': int,
            'rework_tests': int,
            'error': str/None
        }
    """
    result = {
        "success": False,
        "test_results": [],
        "total_tests": 0,
        "failed_tests": 0,
        "rework_tests": 0,
        "error": None,
    }

    try:
        meol_query = """
        SELECT SerialNumber, ItemNo, ProductionVersion, AssetName, EntryCreatedAt, 
               Passed, TestBayNum, Rework, ModuleBrickVoltage, ModuleGroup, 
               ModuleTstRsltDesc, TestResultCode
        FROM TestResults_FullMEOL 
        WHERE SerialNumber = ?
        ORDER BY EntryCreatedAt DESC
        """

        meol_data = system.db.runPrepQuery(meol_query, [module_serial], reporting_db)

        for row in meol_data:
            test_result = {
                "serial": module_serial,
                "item_no": row.get("ItemNo"),
                "product_version": row.get("ProductionVersion"),
                "asset_name": row["AssetName"],
                "test_bay_num": row.get("TestBayNum"),
                "passed": bool(row["Passed"]),
                "test_time_est": convert_to_est(row["EntryCreatedAt"]),
                "is_rework": bool(row.get("Rework")),
                "brick_voltage": row.get("ModuleBrickVoltage"),
                "module_group": row.get("ModuleGroup"),
                "test_description": row.get("ModuleTstRsltDesc"),
                "test_result_code": row.get("TestResultCode"),
            }

            result["test_results"].append(test_result)
            result["total_tests"] += 1

            if not test_result["passed"]:
                result["failed_tests"] += 1
            if test_result["is_rework"]:
                result["rework_tests"] += 1

        result["success"] = True

    except Exception as e:
        result["error"] = "Error checking Full MEOL results: {}".format(str(e))
        logger.error("Error checking Full MEOL results: {}".format(str(e)))

    return result


def full_health_check(module_serial, revmes_db="REVMES", reporting_db="REPORTINGDB"):
    """
    Perform complete health check on module including genealogy, NCF, and all test results

    Returns:
        JSON string with comprehensive results
    """
    logger.info("Starting full health check for module: {}".format(module_serial))

    # Initialize overall results structure
    health_results = {
        "module_serial": module_serial,
        "check_timestamp": convert_to_est(datetime.now()),
        "overall_status": "UNKNOWN",
        "genealogy": {},
        "ncf_status": {},
        "test_results": {"mad_dispense": {}, "mad_cure": {}, "full_meol": {}},
        "summary": {
            "issues": [],
            "warnings": [],
            "total_checks": 0,
            "passed_checks": 0,
        },
    }

    try:
        # 1. Check Genealogy
        logger.info("Checking genealogy for {}".format(module_serial))
        genealogy_result = check_genealogy(module_serial, revmes_db)
        health_results["genealogy"] = genealogy_result
        health_results["summary"]["total_checks"] += 1

        if genealogy_result["success"]:
            if genealogy_result["genealogy_valid"]:
                health_results["summary"]["passed_checks"] += 1
            else:
                health_results["summary"]["issues"].extend(
                    [
                        "Missing components: {}".format(
                            ", ".join(genealogy_result["missing_components"])
                        )
                    ]
                )
        else:
            health_results["summary"]["issues"].append(
                "Genealogy check failed: {}".format(
                    genealogy_result.get("error", "Unknown error")
                )
            )

        # Get all serials for NCF and dispense checks
        all_serials = [module_serial]
        if genealogy_result["success"]:
            for component in genealogy_result["components"].values():
                if component:
                    all_serials.append(component)

        # 2. Check NCF Status
        logger.info("Checking NCF status for {} serials".format(len(all_serials)))
        ncf_result = check_ncf_status(all_serials, revmes_db)
        health_results["ncf_status"] = ncf_result
        health_results["summary"]["total_checks"] += 1

        if ncf_result["success"]:
            if not ncf_result["has_active_ncf"]:
                health_results["summary"]["passed_checks"] += 1
            else:
                health_results["summary"]["issues"].append(
                    "Active NCFs found: {} total".format(ncf_result["active_ncf_count"])
                )
        else:
            health_results["summary"]["issues"].append(
                "NCF check failed: {}".format(ncf_result.get("error", "Unknown error"))
            )

        # 3. Check MAD Dispense Results
        logger.info("Checking MAD Dispense results")
        dispense_result = check_mad_dispense_results(all_serials, reporting_db)
        health_results["test_results"]["mad_dispense"] = dispense_result
        health_results["summary"]["total_checks"] += 1

        if dispense_result["success"]:
            if dispense_result["failed_tests"] == 0:
                health_results["summary"]["passed_checks"] += 1
            else:
                health_results["summary"]["issues"].append(
                    "MAD Dispense failures: {} of {} tests".format(
                        dispense_result["failed_tests"], dispense_result["total_tests"]
                    )
                )
        else:
            health_results["summary"]["issues"].append(
                "MAD Dispense check failed: {}".format(
                    dispense_result.get("error", "Unknown error")
                )
            )

        # 4. Check MAD Cure Results
        logger.info("Checking MAD Cure results")
        cure_result = check_mad_cure_results(module_serial, reporting_db)
        health_results["test_results"]["mad_cure"] = cure_result
        health_results["summary"]["total_checks"] += 1

        if cure_result["success"]:
            if cure_result["failed_tests"] == 0:
                health_results["summary"]["passed_checks"] += 1
            else:
                health_results["summary"]["issues"].append(
                    "MAD Cure failures: {} of {} tests".format(
                        cure_result["failed_tests"], cure_result["total_tests"]
                    )
                )
        else:
            health_results["summary"]["issues"].append(
                "MAD Cure check failed: {}".format(
                    cure_result.get("error", "Unknown error")
                )
            )

        # 5. Check Full MEOL Results
        logger.info("Checking Full MEOL results")
        meol_result = check_full_meol_results(module_serial, reporting_db)
        health_results["test_results"]["full_meol"] = meol_result
        health_results["summary"]["total_checks"] += 1

        if meol_result["success"]:
            if meol_result["failed_tests"] == 0:
                health_results["summary"]["passed_checks"] += 1
            else:
                health_results["summary"]["issues"].append(
                    "Full MEOL failures: {} of {} tests".format(
                        meol_result["failed_tests"], meol_result["total_tests"]
                    )
                )

            # Add warning for rework units
            if meol_result["rework_tests"] > 0:
                health_results["summary"]["warnings"].append(
                    "Module has {} rework test(s)".format(meol_result["rework_tests"])
                )
        else:
            health_results["summary"]["issues"].append(
                "Full MEOL check failed: {}".format(
                    meol_result.get("error", "Unknown error")
                )
            )

        # Determine overall status
        if len(health_results["summary"]["issues"]) == 0:
            health_results["overall_status"] = "PASS"
        else:
            health_results["overall_status"] = "FAIL"

        logger.info(
            "Health check completed for {}: {} ({} issues)".format(
                module_serial,
                health_results["overall_status"],
                len(health_results["summary"]["issues"]),
            )
        )

    except Exception as e:
        error_msg = "Critical error during health check: {}".format(str(e))
        logger.error(error_msg)
        health_results["summary"]["issues"].append(error_msg)
        health_results["overall_status"] = "ERROR"

    # Return JSON string
    return json.dumps(health_results, indent=2)


# Individual function wrappers that return JSON for easy integration
def get_genealogy_json(module_serial, revmes_db="REVMES"):
    """Return genealogy check results as JSON string"""
    return json.dumps(check_genealogy(module_serial, revmes_db), indent=2)


def get_ncf_status_json(all_serials, revmes_db="REVMES"):
    """Return NCF status check results as JSON string"""
    return json.dumps(check_ncf_status(all_serials, revmes_db), indent=2)


def get_mad_dispense_json(all_serials, reporting_db="REPORTINGDB"):
    """Return MAD Dispense results as JSON string"""
    return json.dumps(check_mad_dispense_results(all_serials, reporting_db), indent=2)


def get_mad_cure_json(module_serial, reporting_db="REPORTINGDB"):
    """Return MAD Cure results as JSON string"""
    return json.dumps(check_mad_cure_results(module_serial, reporting_db), indent=2)


def get_full_meol_json(module_serial, reporting_db="REPORTINGDB"):
    """Return Full MEOL results as JSON string"""
    return json.dumps(check_full_meol_results(module_serial, reporting_db), indent=2)


# Example usage:
def example_usage():
    """Example of how to use the functions"""
    module_serial = "M123456789AB"

    # Get complete health check
    complete_results = full_health_check(module_serial)

    # Or get individual checks
    genealogy_results = get_genealogy_json(module_serial)

    return complete_results
