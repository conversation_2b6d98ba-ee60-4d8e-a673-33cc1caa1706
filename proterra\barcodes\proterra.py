from . import common as common_barcodes


def validate_proterra_barcode(barcode):
    """
    Validate a proterra barcode and return its serial number, part number, revision, facility, item version id, and search string

    :param barcode: The barcode string
    :type barcode: str
    :returns: A tuple containing the serial number, part number, revision, facility, item version id, and search string
    :rtype: tuple
    :raises ValueError: If the barcode is invalid or item version not found
    :raises ValueError: If the barcode does not match the expected format
    :raises ValueError: If the barcode contains non-ASCII printable characters or is empty
    """
    serial = ''
    part = ''
    rev = ''
    fac = ''
    item_version_id = -1

    # Validate the common structure of the barcode
    barcode_splits = common_barcodes.validate(barcode)

    # Check if the barcode has 4 parts
    if len(barcode_splits) == 4:
        # The barcode is in the Proterra format with 4 parts
        # i.e. 199-4631 | A01 | GVB | M2432720000189
        serial = barcode_splits[3]  # M2432720000189
        part = barcode_splits[0]  # 199-4631
        rev = barcode_splits[1]  # A01
        fac = barcode_splits[2]  # GVB
        item_version_id, part, rev = common_barcodes.get_item_version_id(part, rev)
    else:
        # The barcode is invalid
        raise ValueError('Invalid cassette sleeve barcode format')

    # Check if the item version id was found
    if item_version_id == -1 or not part or not rev:
        raise ValueError('Item version not found for part: {part}, revision: {rev}'.format(part=part, rev=rev))

    # Return the serial number, part number, revision, facility, item version id, and search string
    return serial, part, rev, fac, item_version_id, common_barcodes.get_search_string(barcode_splits)
