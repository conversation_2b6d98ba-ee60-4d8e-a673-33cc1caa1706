# -------------------
# Weld Schedule Script thing to move the stuff from there to other there
# THWI 2/19/25 -- Ver. 1.1.8
# - Fixed : everything - disabled info logs
# -------------------

# Import + hope they avail in ign?
from java.lang import Exception as JavaException
import traceback

# Logger
fnname = "processWeldData"
fnlogger = system.util.getLogger(fnname)

# Target Acquisition
sourceDb = "Welder_sPK_DB"  # Source
targetDb = "LWMDB"  # Target

# Query

idsQuery = """
SELECT id, "serialNumber"
FROM "FinishedParts"
WHERE "runFinish" BETWEEN ?::TIMESTAMP AND ?::TIMESTAMP
LIMIT ?;
"""

jsonQuery = """
SELECT name, value
FROM json_to_recordset(
	(SELECT "partDataPropertiesAttributesJson"->'attributesJson'
	FROM "FinishedParts"
	WHERE id = ?::INT)
) AS x(name TEXT, value TEXT);
"""

# Query Param
startTime = system.date.parse(
    system.date.format(
        system.date.addDays(system.date.now(), -1), "yyyy-MM-dd HH:mm:ss"
    ),
    "yyyy-MM-dd HH:mm:ss",
)
endTime = system.date.parse(
    system.date.format(system.date.now(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"
)
queryLimit = 100  # batch


# Mappings
fieldDefinitions = {
    "WeldInfo": {
        "fields": {
            "KeyenceMetrologyResultZ": 254,
            "State": 1,
            "QualityState": 254,
            "VisionfieldId": 2,
            "VisionfieldX": 2,
            "VisionfieldY": 2,
            "ScanfieldId": 254,
            "Rework": 254,
            "MetrologyObject": 2,
            "MetrologyResultX": 2,
            "MetrologyResultY": 2,
        }
    },
    "ErrorTop": {
        "fields": {
            "ErrorFrequencyTop1": 254,
            "ErrorFrequencyTop2": 38,
            "ErrorFrequencyTop3": 38,
            "ErrorFrequencyTop4": 38,
            "ErrorFrequencyTop5": 38,
            "ErrorFrequencyTop6": 36,
            "ErrorFrequencyTop7": 36,
            "ErrorFrequencyTop8": 36,
            "ErrorFrequencyTop9": 36,
            "ErrorFrequencyTop10": 36,
        }
    },
    "ErrorBottom": {
        "fields": {
            "ErrorFrequencyBottom1": 254,
            "ErrorFrequencyBottom2": 38,
            "ErrorFrequencyBottom3": 38,
            "ErrorFrequencyBottom4": 38,
            "ErrorFrequencyBottom5": 38,
            "ErrorFrequencyBottom6": 36,
            "ErrorFrequencyBottom7": 36,
            "ErrorFrequencyBottom8": 36,
            "ErrorFrequencyBottom9": 36,
            "ErrorFrequencyBottom10": 36,
        }
    },
    "SdmWeld": {
        "fields": {
            "SdmWeld1": 254,
            "SdmWeld2": 38,
            "SdmWeld3": 38,
            "SdmWeld4": 38,
            "SdmWeld5": 38,
            "SdmWeld6": 36,
            "SdmWeld7": 36,
            "SdmWeld8": 36,
            "SdmWeld9": 36,
            "SdmWeld10": 36,
        }
    },
}


def parseSerialNumber(serialNumber):
    """Extract the serial number."""
    if serialNumber and "_" in serialNumber:
        return serialNumber.split("_")[0]
    return serialNumber  # Return as is if no `_` found


def get_manz_data(partId):
    """Fetch and convert JSON"""
    # fnlogger.info("Running JSON query for PartId {}...".format(partId))
    try:
        result = system.db.runPrepQuery(jsonQuery, [int(partId)], sourceDb)

        if result.getRowCount() == 0:
            fnlogger.warn("No data found for PartId {}".format(partId))
            return {}

        # Convert to python dict
        data = {
            result.getValueAt(i, "name"): result.getValueAt(i, "value")
            for i in range(result.getRowCount())
        }

        pictureName = data.get("PictureName", None)
        if pictureName is None:
            pictureName = "Unknown"
        # fnlogger.info("JSON data for PartId {}: {} keys".format(partId, len(data)))
        return data

    except (Exception, JavaException):
        msg = traceback.format_exc()
        fnlogger.error("Error retrieving JSON for PartId {}: {}".format(partId, msg))
        return {}


def generateFieldData(jsonData, fields, partId, serialNumber):
    """Generate field data from JSON attributes."""
    fieldData = []
    for field, count in fields.items():
        for i in range(1, count + 1):
            indexedField = "{}[{}]".format(field, i)
            if indexedField in jsonData:
                fieldData.append(
                    {
                        "PartId": int(partId),
                        "SerialNumber": str(serialNumber),
                        "Field": str(field),
                        "[Index]": int(i),
                        "Value": str(jsonData[indexedField]),
                    }
                )
    # fnlogger.info("Generated Field Data for part {}".format(partId))
    return fieldData


def insertBatch(data, tableName):
    """Insert a batch of data."""
    if not data:
        return "No data to insert"

    txIns = system.db.beginTransaction(targetDb, timeout=240000)
    try:
        # Extract column names and prepare the insert query
        columns = ", ".join(data[0].keys())
        placeholders = ", ".join(["?"] * len(data[0]))
        query = """
			INSERT INTO {db}.dbo.{tableName} ({columns})
			VALUES ({placeholders})
		""".format(
            db=targetDb, tableName=tableName, columns=columns, placeholders=placeholders
        )

        # Execute insert for each row in data
        for row in data:
            system.db.runPrepUpdate(query, list(row.values()), tx=txIns)

        # Commit transaction after successful inserts
        system.db.commitTransaction(txIns)

        # fnlogger.info("Inserted {} rows into {}".format(len(data), tableName))
        return "Success"

    except (Exception, JavaException) as e:
        msg = traceback.format_exc()
        fnlogger.error("Error inserting rows into {}: {}".format(tableName, msg))
        system.db.rollbackTransaction(txIns)
        raise

    finally:
        system.db.closeTransaction(txIns)

    return "Data inserted successfully into {}".format(tableName)


def logProcessedSerial(serialNumber, partId, pictureName, processedSerials):
    """Logs processed serials to avoid duplicates."""
    if not processedSerials:
        return "No serials to log"
    txLog = system.db.beginTransaction(targetDb, timeout=240000)
    try:
        query = """
		INSERT INTO LWMDB.dbo.ProcessedSerials (SerialNumber, PartId, PictureName, ProcessedAt)
		VALUES (?, ?, ?, ?);
		"""

        timestamp = system.date.now()

        for serial in processedSerials:
            system.db.runPrepUpdate(query, serial + [timestamp], tx=txLog)
        # system.db.runPrepUpdate(query, [serialNumber, partId, pictureName], targetDb)
        system.db.commitTransaction(txLog)
        # fnlogger.info("Logged SerialNumber {} with partId {} as processed.".format(serialNumber, partId))
    except Exception as e:
        fnlogger.error("Error logging SerialNumber {}: {}".format(serialNumber, str(e)))
        system.db.rollbackTransaction(txLog)
    return "Logged {} processed serials.".format(len(processedSerials))


def processWeldData():
    """Main function to process weld data."""
    fnlogger.info("Starting Weld Data Processing")
    processedSerials = []
    try:
        # fnlogger.info("Fetching Part IDs and Serial Numbers...")
        tx2 = system.db.beginTransaction(sourceDb, timeout=240000)
        ids = system.db.runPrepQuery(idsQuery, [startTime, endTime, queryLimit], tx=tx2)
        system.db.commitTransaction(tx2)
        system.db.closeTransaction(tx2)

        if ids.getRowCount() == 0:
            # fnlogger.info("No records found for the given time range.")
            return "No data found"

        fnlogger.info("Retrieved {} Part IDs.".format(ids.getRowCount()))
        tableBatches = {table: [] for table in fieldDefinitions.keys()}

        for row in range(ids.getRowCount()):
            partId = int(ids.getValueAt(row, 0))
            serialNumberRaw = ids.getValueAt(row, 1)
            serialNumber = parseSerialNumber(serialNumberRaw)

            # Check if partId already exists in ProcessedSerials
            check_query = (
                "SELECT COUNT(*) FROM LWMDB.dbo.ProcessedSerials WHERE PartId = ?;"
            )
            exists = system.db.runScalarPrepQuery(check_query, [partId], targetDb)

            if exists > 0:
                # fnlogger.info("Skipping already processed PartID {} for Serial {}".format(partId, serialNumber))
                continue  # Skip processing if already in ProcessedSerials

            # fnlogger.info("Processing PartID {} Serial {}".format(partId, serialNumber))

            jsonData = get_manz_data(partId)
            if not jsonData:
                # fnlogger.info("No JSON data for PartID {}".format(partId))
                continue

            # Extract PictureName from json
            pictureName = jsonData.get("PictureName", "Unknown")
            processedSerials.append([serialNumber, partId, pictureName])

            # Generate batch data
            for table, definition in fieldDefinitions.items():
                tableBatches[table].extend(
                    generateFieldData(
                        jsonData, definition["fields"], partId, serialNumber
                    )
                )
        # Log processed serialnumbers
        if processedSerials:
            logProcessedSerial(serialNumber, partId, pictureName, processedSerials)

        for table, batch in tableBatches.items():
            if batch:
                # fnlogger.info("Starting batch insert for {}".format(table))
                insertBatch(batch, table)

    except (Exception, JavaException) as e:
        msg = traceback.format_exc()
        fnlogger.error("Critical error, rolling back: " + msg)

    fnlogger.info("Weld Data Processing Completed.")
    return "Success"
