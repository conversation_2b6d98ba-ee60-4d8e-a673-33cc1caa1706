.ia_qualityOverlay {
    border-width: 1.5px;
}

.ia_qualityOverlay--error {
    border-color: var(--error);
    border-style: solid;
    border-bottom: none;
}

.ia_qualityOverlay--error.micro {
    border-bottom: 1.5px solid var(--error);
}

.ia_qualityOverlay--unknown {
    border-color: var(--neutral-100);
    border-style: dotted;
    border-bottom: none;
}

.ia_qualityOverlay--unknown.micro {
    border-bottom: 1.5px dotted var(--neutral-100);
}

.ia_qualityOverlay--pending {
    border-color: var(--indicator);
    border-style: dashed;
    border-bottom: none;
}

.ia_qualityOverlay--pending.micro {
    border-bottom: 1.5px dashed var(--indicator);
}

.ia_qualityOverlay__footer {
    color: var(--neutral-10);
}

.ia_qualityOverlay__footer--error {
    background-color: var(--error);
}

.ia_qualityOverlay__footer--unknown {
    background-color: var(--neutral-100);
    color: var(--neutral-10);
}

.ia_qualityOverlay__footer--pending {
    background-color: var(--indicator);
}

.ia_qualityOverlay__icon__outline {
    fill: var(--containerRoot);
}

.ia_qualityOverlay__icon--error {
    color: var(--neutral-10);
    fill: var(--error);
}

.ia_qualityOverlay__icon--pending {
    color: var(--neutral-10);
    fill: var(--indicator);
}

.ia_qualityOverlay__icon--unknown {
    color: var(--neutral-10);
    fill: var(--neutral-100);
}
.ia_qualityOverlay__icon--micro--error,
.ia_qualityOverlay__icon--micro--pending,
.ia_qualityOverlay__icon--micro--unknown {
    border: white solid 1px;
}

.ia_qualityOverlay__icon--micro--error {
    background-color: var(--error);
}

.ia_qualityOverlay__icon--micro--pending {
    background-color: var(--indicator);
}

.ia_qualityOverlay__icon--micro--unknown {
    background-color: var(--neutral-100);
}
