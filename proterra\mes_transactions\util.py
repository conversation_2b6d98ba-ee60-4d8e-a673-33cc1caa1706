import traceback
import system.date
import system.util


def convertResultToJSON(dataSet):
    resultDict = {}
    for columnName in dataSet.getColumnNames():
        resultDict[columnName] = dataSet.getValueAt(0, dataSet.getColumnIndex(columnName))
    return resultDict


def parseBarcode_Last(barcode):
    bcList = barcode.split('|')

    # If new format (with 6 parts), combine the last two elements
    if len(bcList) == 6:
        bcList[4] = bcList[4] + bcList[5]
        bcList = bcList[:5]  # Remove the last element (now combined)

    barcode = bcList[-1]
    return str(barcode.strip().replace(' ', '').replace('\x00', ''))


def parseBarcode_Foil(barcode):
    return str('|'.join(barcode.split('|')[-2:]))


def getTXNInputValues(txnInputs, baseTagPath):
    # used on:
    # REVMES/OperatorStation/NegWeldPrep
    # REVMES/OperatorStation/PosWeldPrep
    # REVMES/OperatorStation/QC
    tagsRead = system.tag.readBlocking([baseTagPath+i[1] for i in txnInputs])  # reads, basepath+tags
    txnInputVal = {txnInputs[e][0]: tr.value for e, tr in enumerate(tagsRead)}  # dict of ref:values
    return txnInputVal


def writeResponse(pathRESP, sprocResults):
    # used on:
    # REVMES/OperatorStation/NegWeldPrep
    # REVMES/OperatorStation/PosWeldPrep
    # REVMES/OperatorStation/QC
    try:
        if sprocResults["Success"]:
            paths = [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status']
            values = [sprocResults['RespSeries'], sprocResults['RespType'], sprocResults['RespStatus']]
            writes = system.tag.writeBlocking(paths, values)
        else:
            paths = [pathRESP+'/Series', pathRESP+'/Type', pathRESP+'/Status']
            values = [sprocResults['RespSeries'], sprocResults['RespType'], sprocResults['ResultCode']]
            writes = system.tag.writeBlocking(paths, values)
    except:
        exc = traceback.format_exc()
        DataAccess.Log.LogError(message=exc, functionName="writeResponse", application="proterra.mes_transactions.util")
    else:
        badWriteInfo = [str(p)+':'+str(w.quality) for p, w in zip(paths, writes) if not w.isGood()]
        if len(badWriteInfo) > 0:
            DataAccess.Log.LogError(message='Failed to Write: '+', '.join(badWriteInfo),
                                    functionName="writeResponse", application="proterra.mes_transactions.util")
