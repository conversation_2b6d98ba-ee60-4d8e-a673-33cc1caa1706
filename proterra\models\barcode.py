from java.lang import Exception as JavaException
from proterra.models import serial_number
from socket import socket, AF_INET, SOCK_STREAM
import traceback
import system.db
import system.tag
import system.util

_logger = system.util.getLogger('proterra.models.barcode')


class BarcodeException(Exception):
    """
    Exception that can be raised and caught for barcode errors
    """
    pass


class RawBarcode:
    """
    ORM representation of the [MESDB].[mes].[RawBarcodes] table
    """
    SerialNoNumber = ''
    RawBarcode = ''


def get_serial(barcode):
    """
    Get a serial string from the provided barcode
    Note: Only use this method for retrieving barcodes. To store a vendor barcode, use the method that corresponds to
    the correct type of barcode.

    :param barcode: The barcode string
    :returns: The serial string
    """
    # Make sure the barcode is not empty
    if not barcode:
        return
    serial = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 7:
        if len(no_ws_barcode) >= 43:
            # The barcode is a cassette vendor barcode
            serial = get_cassette_serial(barcode)
    elif barcode.count('|') == 5:
        if len(no_ws_barcode) >= 40:
            # The barcode is a foil vendor barcode
            serial = get_foil_serial(barcode)
        elif len(no_ws_barcode) >= 32:
            # The barcode is a spine vendor barcode
            serial = get_spine_serial(barcode)
    elif barcode.count('|') == 4:
        if len(no_ws_barcode) >= 39:
            # The barcode is a foil vendor barcode
            serial = get_foil_serial(barcode)
    elif barcode.count('|') == 3:
        if len(no_ws_barcode) >= 28:
            # The barcode is a Proterra barcode
            serial = __get_proterra_serial(barcode)
    elif barcode.count('|') == 0:
        if len(no_ws_barcode) >= 27 and no_ws_barcode[7] == '-':
            # The barcode is a spine vendor barcode
            serial = get_spine_serial(barcode)
        elif len(no_ws_barcode) >= 10 and no_ws_barcode[4:10] == '052550':
            # The barcode is a cassette vendor barcode
            serial = get_cassette_serial(barcode)
        elif len(no_ws_barcode) >= 4 and no_ws_barcode[:4] == '0000':
            # The barcode is a BMB vendor barcode
            serial = get_bmb_serial(barcode)
    # Check if we were able to parse the barcode
    if not serial and barcode.count('|') > 0:
        # Replace | with %
        like = barcode.replace('|', '%')
        # Replace _ with %
        like = like.replace('_', '%')
        # Replace / with %
        like = like.replace('/', '%')
        # Remove any whitespace in the string
        like = ''.join(like.split())
        # Surround with %
        like = '%{}%'.format(like)
        # Define the query
        query = '''
            SELECT TOP(1) [rb].[SerialNoNumber]
            FROM [REVMES].[mes].[RawBarcodes] rb
            WHERE [rb].[RawBarcode] LIKE ?;
        '''
        # Define the arguments
        args = [like]
        try:
            # Run the query
            result = system.db.runScalarPrepQuery(query, args, 'REVMES_Replica')
            # Get the result
            serial = str(result) if result else ''
        except (Exception, JavaException):
            _logger.error(traceback.format_exc())
    if not serial:
        # Return the full barcode as the serial
        if barcode.count('|') < 3:
            barcode = barcode.replace('|', '')
        serial = ''.join(barcode.split())
    return serial


def get_barcode(serial):
    """
    Get the barcode string from the provided serial string

    :param serial: The serial string
    :returns: The barcode string
    """
    # Make sure the serial is not empty
    if not serial:
        return
    barcode = ''
    # Define the query
    query = '''
        SELECT TOP(1) [rb].[RawBarcode]
        FROM [REVMES].[mes].[RawBarcodes]
        WHERE [SerialNoNumber]=?;
    '''
    # Define the arguments
    args = [serial]
    try:
        # Run the query
        result = system.db.runScalarPrepQuery(query, args, 'REVMES_Replica')
        # Get the result
        barcode = str(result) if result else ''
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return barcode


def get_part_number(barcode):
    """
    Get a part number string from the provided barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    # Make sure the barcode is not empty
    if not barcode:
        return
    part_number = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 7:
        if len(no_ws_barcode) >= 43:
            # The barcode is a cassette vendor barcode
            part_number = __get_cassette_part_number(barcode)
    elif barcode.count('|') == 5:
        if len(no_ws_barcode) >= 40:
            # The barcode is a foil vendor barcode
            part_number = __get_foil_part_number(barcode)
        elif len(no_ws_barcode) >= 32:
            # The barcode is a spine vendor barcode
            part_number = __get_spine_part_number(barcode)
    elif barcode.count('|') == 4:
        if len(no_ws_barcode) >= 39:
            # The barcode is a foil vendor barcode
            part_number = __get_foil_part_number(barcode)
    elif barcode.count('|') == 3:
        if len(no_ws_barcode) >= 28:
            # The barcode is a Proterra barcode
            part_number = __get_proterra_part_number(barcode)
    elif barcode.count('|') == 0:
        if len(no_ws_barcode) >= 27 and no_ws_barcode[7] == '-':
            # The barcode is a spine vendor barcode
            part_number = __get_spine_part_number(barcode)
        elif len(no_ws_barcode) >= 10 and no_ws_barcode[4:10] == '052550':
            # The barcode is a cassette vendor barcode
            part_number = __get_cassette_part_number(barcode)
        elif len(no_ws_barcode) >= 4 and no_ws_barcode[:4] == '0000':
            # The barcode is a BMB vendor barcode
            part_number = __get_bmb_part_number(barcode)
    else:
        _logger.error('Cannot determine barcode type for {}'.format(barcode))
    return part_number


def get_rev(barcode):
    """
    Get a rev string from the provided barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    # Make sure the barcode is not empty
    if not barcode:
        return
    part_number = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 7:
        if len(no_ws_barcode) >= 43:
            # The barcode is a cassette vendor barcode
            part_number = __get_cassette_rev(barcode)
    elif barcode.count('|') == 5:
        if len(no_ws_barcode) >= 40:
            # The barcode is a foil vendor barcode
            part_number = __get_foil_rev(barcode)
        elif len(no_ws_barcode) >= 32:
            # The barcode is a spine vendor barcode
            part_number = __get_spine_rev(barcode)
    elif barcode.count('|') == 4:
        if len(no_ws_barcode) >= 39:
            # The barcode is a foil vendor barcode
            part_number = __get_foil_rev(barcode)
    elif barcode.count('|') == 3:
        if len(no_ws_barcode) >= 28:
            # The barcode is a Proterra barcode
            part_number = __get_proterra_rev(barcode)
    elif barcode.count('|') == 0:
        if len(no_ws_barcode) >= 27 and no_ws_barcode[7] == '-':
            # The barcode is a spine vendor barcode
            part_number = __get_spine_rev(barcode)
        elif len(no_ws_barcode) >= 10 and no_ws_barcode[4:10] == '052550':
            # The barcode is a cassette vendor barcode
            part_number = __get_cassette_rev(barcode)
        elif len(no_ws_barcode) >= 4 and no_ws_barcode[:4] == '0000':
            # The barcode is a BMB vendor barcode
            part_number = __get_bmb_rev(barcode)
    else:
        _logger.error('Cannot determine barcode type for {}'.format(barcode))
    return part_number


def get_facility(barcode):
    """
    Get a facility string from the provided barcode

    :param barcode: The barcode string
    :returns: The facility string
    """
    # Make sure the barcode is not empty
    if not barcode:
        return
    part_number = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 3:
        if len(no_ws_barcode) >= 28:
            # The barcode is a Proterra barcode
            part_number = __get_proterra_facility(barcode)
    else:
        _logger.error('Cannot determine barcode type for {}'.format(barcode))
    return part_number


def get_proterra_barcode_parts(barcode):
    """
    Get all the parts of a proterra barcode

    :param barcode: The barcode string
    :returns: The part number, rev, facility, and serial number parts of a Proterra barcode
    """
    # Make sure the format is correct
    if barcode.count('|') != 3:
        raise BarcodeException('Barcode is not in the correct format')
    # Split the barcode
    parts = barcode.split('|')
    # Return the splits
    return parts[0].strip(), parts[1].strip(), parts[2].strip(), parts[3].strip()


def get_proterra_barcode_from_vendor(barcode):
    """
    Get a Proterra barcode from the vendor barcode if the item has been consumed

    :param barcode: The vendor barcode string
    :returns: The Proterra barcode string
    """
    # Get the serial
    vendor_sn = get_serial(barcode)
    if not vendor_sn:
        return ''
    part_no, rev, facility, serial = ''
    # Define the query
    query = '''
        SELECT TOP(1) iv.ItemNo,iv.ProductionVersion,'GVB' AS Facility,isn2.SerialNoNumber
        FROM [MESDB].[mes].[Item_SerialNo] isn1
        JOIN [MESDB].[mes].[Item_Consumption] ic
        ON isn1.ItemSerialNoId=ic.FromSerialNoId
        JOIN [MESDB].[mes].[Item_SerialNo] isn2
        ON ic.ToSerialNoId=isn2.ItemSerialNoId
        JOIN [MESDB].[product].[Item_Version] iv
        ON isn2.ItemVersionId=iv.ItemVersionId
        WHERE isn1.SerialNoNumber=?;
    '''
    # Define the arguments
    args = [vendor_sn]
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
        # Get the results
        if results:
            ItemNo = results[0]['ItemNo']
            ProductionVersion = results[0]['ProductionVersion']
            Facility = results[0]['Facility']
            SerialNoNumber = results[0]['SerialNoNumber']
            # Convert the return values
            part_no = str(ItemNo) if ItemNo else ''
            rev = str(ProductionVersion) if ProductionVersion else ''
            facility = str(Facility) if Facility else ''
            serial = str(SerialNoNumber) if SerialNoNumber else ''
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    # Return the Proterra barcode
    return part_no, rev, facility, serial


def get_bmb_serial(barcode, store=False, tx=None):
    """
    Get a serial string from the barcode string

    :param barcode: The barcode string
    :param store: True to store a link between the serial and barcode
    :param tx: An optional transaction
    :returns: The serial string
    """
    serial = ''
    # This is the vendor barcode on the BMB
    if len(barcode) == 36:
        # The barcode is in a format with 0 delimiters (1 section)
        # i.e. 000017425942312304521301002375111E01
        # Use the entire barcode (index 0) as the serial number
        serial = __get_barcode_parts(barcode, 1, (0,))
    # Store the serial barcode
    if store:
        __store_serial_barcode(serial, barcode, tx)
    # Return the serial
    return serial


def get_cassette_serial(barcode, store=False, tx=None):
    """
    Get a serial string from the barcode string

    :param barcode: The barcode string
    :param store: True to store a link between the serial and barcode
    :param tx: An optional transaction
    :returns: The serial string
    """
    serial = ''
    # Determine which version of the cassette barcode we need to handle
    # This is the vendor barcode on the cassette
    if barcode.count('|') == 7:
        # The barcode is in the new format with 7 delimiters (8 sections)
        # i.e. 1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01
        # Use the entire barcode without delimiters as the serial number
        serial = __get_barcode_parts(barcode, 8, range(0, 8)).replace('/', '')
    else:
        # The barcode is in the old format with 0 delimiters (1 section)
        # i.e. 1599052550C0209152024K24A1840000002329760
        # Use the entire barcode (index 0) as the serial number
        # TODO: Remove this when this type of barcode is no longer in use
        serial = __get_barcode_parts(barcode, 1, (0,))
    # Store the serial barcode
    if store:
        __store_serial_barcode(serial, barcode, tx)
    # Return the serial
    return serial


def get_foil_serial(barcode, store=False, tx=None):
    """
    Get a serial string from the barcode string

    :param barcode: The barcode string
    :param store: True to store a link between the serial and barcode
    :param tx: An optional transaction
    :returns: The serial string
    """
    serial = ''
    # Determine which version of the foil barcode we need to handle
    # This is the vendor barcode on the foil
    if barcode.count('|') == 5:
        # The barcode is in the new format with 5 delimiters (6 sections)
        # i.e. 3661 | 133-0447 | C.1 | 241028 | K024A21001 | 0551
        # Use the last 2 sections (indices 4 and 5) as the serial number (remove whitespace)
        serial = __get_barcode_parts(barcode, 6, (4, 5))
    elif barcode.count('|') == 4:
        # The barcode is in the old format with 4 delimiters (5 sections)
        # i.e. 3661 | 133-0468 | C.1 | 102723 | K023A23001 1432
        # Use the last section (index 4) as the serial number (remove whitespace)
        # TODO: Remove this when this type of barcode is no longer in use
        serial = __get_barcode_parts(barcode, 5, (4,))
    # Store the serial barcode
    if store:
        __store_serial_barcode(serial, barcode, tx)
    # Return the serial
    return serial


def get_spine_serial(barcode, store=False, tx=None):
    """
    Get a serial string from the barcode string

    :param barcode: The barcode string
    :param store: True to store a link between the serial and barcode
    :param tx: An optional transaction
    :returns: The serial string
    """
    serial = ''
    # Determine which version of the spine barcode we need to handle
    # This is the vendor barcode on the spine
    if barcode.count('|') == 5:
        # The barcode is in the new format with 5 delimiters (6 sections)
        # i.e. 4222 | 195-3560 | A.1 | JT | 08302024 | 0022
        # Use the entire barcode without delimiters as the serial number
        serial = __get_barcode_parts(barcode, 6, range(0, 6))
    elif len(barcode) >= 20:
        # The barcode is in the old format with 0 delimiters (1 section)
        # i.e. 4222187-5071A.1061020230285
        # Use the entire barcode (index 0) as the serial number
        # TODO: Remove this when this type of barcode is no longer in use
        serial = __get_barcode_parts(barcode, 1, (0,)).replace('_', '')
    # Store the serial barcode
    if store:
        __store_serial_barcode(serial, barcode, tx)
    # Return the serial
    return serial


def __get_bmb_part_number(barcode):
    """
    Get the part number string from the barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    part_no = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    # Check the barcode length
    if len(no_ws_barcode) == 36:
        # Get the part number of the barcode
        part_no = '{}-{}'.format(no_ws_barcode[4:7], no_ws_barcode[7:11])
    return part_no


def __get_bmb_rev(barcode):
    """
    Get the rev string from the barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    rev = ''
    # The rev as defined in Item_Version is not defined in the barcode
    return rev


def __get_cassette_part_number(barcode):
    """
    Get the part number string from the barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    part_no = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 7:
        # The barcode is in the new format with 7 delimiters (8 sections)
        part_no = __get_barcode_parts(barcode, 8, (1,))
    elif len(no_ws_barcode) >= 10:
        # The barcode is in the old format with 0 delimiters (1 section)
        part_no = no_ws_barcode[4:10]
    return part_no


def __get_cassette_rev(barcode):
    """
    Get the rev string from the barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    rev = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 7:
        # The barcode is in the new format with 7 delimiters (8 sections)
        rev = __get_barcode_parts(barcode, 8, (2,))
    elif len(no_ws_barcode) >= 10:
        # The barcode is in the old format with 0 delimiters (1 section)
        rev = no_ws_barcode[10:13]
    return rev


def __get_foil_part_number(barcode):
    """
    Get the part number string from the barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    part_no = ''
    if barcode.count('|') == 5:
        # The barcode is in the new format with 5 delimiters (6 sections)
        part_no = __get_barcode_parts(barcode, 6, (1,))
    elif barcode.count('|') == 4:
        # The barcode is in the old format with 4 delimiters (5 sections)
        part_no = __get_barcode_parts(barcode, 5, (1,))
    return part_no


def __get_foil_rev(barcode):
    """
    Get the rev string from the barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    rev = ''
    if barcode.count('|') == 5:
        # The barcode is in the new format with 5 delimiters (6 sections)
        rev = __get_barcode_parts(barcode, 6, (2,))
    elif barcode.count('|') == 4:
        # The barcode is in the old format with 4 delimiters (5 sections)
        rev = __get_barcode_parts(barcode, 5, (2,))
    return rev


def __get_spine_part_number(barcode):
    """
    Get the part number string from the barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    part_no = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 5:
        # The barcode is in the new format with 5 delimiters (6 sections)
        part_no = __get_barcode_parts(barcode, 6, (1,))
    elif len(no_ws_barcode) >= 12:
        # The barcode is in the old format with 0 delimiters (1 section)
        part_no = no_ws_barcode[4:12]
    return part_no


def __get_spine_rev(barcode):
    """
    Get the rev string from the barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    rev = ''
    # Get the barcode with no whitespace
    no_ws_barcode = ''.join(barcode.split())
    if barcode.count('|') == 5:
        # The barcode is in the new format with 5 delimiters (6 sections)
        rev = __get_barcode_parts(barcode, 6, (2,))
    elif len(no_ws_barcode) >= 12:
        # The barcode is in the old format with 0 delimiters (1 section)
        rev = no_ws_barcode[12:15]
    return rev


def get_pallet_serial(barcode):
    """
    Get a serial string from the barcode string

    :param barcode: The barcode string
    :returns: The serial string
    """
    serial = ''
    # Determine if this is a pallet barcode or serial
    # This is the barcode on the pallet
    if barcode.count('|') == 2:
        # This is a barcode with 2 delimiters (3 sections)
        # i.e. 146-3578 | A.1 | 1166
        # Use the last section (index 2) as the serial number (remove whitespace)
        serial = __get_barcode_parts(barcode, 3, (2,))
    else:
        # This is already a pallet serial number
        # i.e. 1166
        # Use the entire barcode (index 0) as the serial number
        serial = __get_barcode_parts(barcode, 1, (0,))
    # Return the serial
    return serial


def get_pallet_part_number(barcode):
    """
    Get the part number string from the barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    part_no = ''
    if barcode.count('|') == 2:
        # This is a barcode with 2 delimiters (3 sections)
        part_no = __get_barcode_parts(barcode, 3, (0,))
    return part_no


def get_pallet_rev(barcode):
    """
    Get the rev string from the barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    part_no = ''
    if barcode.count('|') == 2:
        # This is a barcode with 2 delimiters (3 sections)
        part_no = __get_barcode_parts(barcode, 3, (1,))
    return part_no


def __get_proterra_serial(barcode):
    """
    Get a serial string from the barcode string

    :param barcode: The barcode string
    :returns: The serial string
    """
    # All barcodes generated by Proterra have a consistent format with 3 delimiters (4 sections)
    # i.e. 199-4631 | A01 | GVB | M2432720000189 = M2432720000189
    # Use the last section (index 3) as the serial number (remove whitespace)
    serial = __get_barcode_parts(barcode, 4, (3,))
    # Return the serial
    return serial


def __get_proterra_part_number(barcode):
    """
    Get the part number string from the barcode

    :param barcode: The barcode string
    :returns: The part number string
    """
    # Get the barcode part number
    return __get_barcode_parts(barcode, 4, (0,))


def __get_proterra_rev(barcode):
    """
    Get the rev string from the barcode

    :param barcode: The barcode string
    :returns: The rev string
    """
    # Get the barcode rev
    return __get_barcode_parts(barcode, 4, (1,))


def __get_proterra_facility(barcode):
    """
    Get the facility string from the barcode

    :param barcode: The barcode string
    :returns: The facility string
    """
    # Get the barcode facility
    return __get_barcode_parts(barcode, 4, (2,))


def __get_barcode_parts(barcode, part_count, part_ids, delimiter='|'):
    """
    Get and combine barcode parts from a raw barcode

    The barcode "4222 | 195-3560 | A.1 | JT | 08302024 | 0022" has 6 parts and we want to combine indices 4 and 5,
    so this function would be called as:

    serial = _get_barcode_parts(6, (4, 5))

    :param part_count: The number of parts between delimiters, 6 for the example barcode
    :param part_ids: The ids of the barcode parts to combine, (4, 5) for the example barcode
    :param delimiter: The delimiter for the barcode, | by default
    :returns: The combined parts, or empty string on error
    """
    # Make sure we have something to split
    if not barcode:
        return
    # Split the entire string based on the delimiter
    splits = barcode.split(delimiter)
    # Validate that the barcode has the specified number of parts
    if len(splits) != part_count:
        # Log the error and return empty string if validation fails
        msg = 'Barcode {} contains {} parts instead of expected {}.'
        _logger.warn(msg.format(barcode, len(splits), part_count))
        return ''
    parts = ''
    # Get the specified parts from the barcode
    for idx in part_ids:
        parts += splits[idx]
    # Sanitize any unicode null values that crept in
    parts = parts.replace('\u0000', '').replace('\x00', '')
    # Return the combined parts
    # Note: The call to .split() here removes any whitespace in the string
    return ''.join(parts.split())


def __store_serial_barcode(serial, barcode, tx=None):
    """
    Store a link between a serial and a barcode

    :param serial: The serial string
    :param barcode: The barcode string
    :param tx: An optional transaction
    """
    # Make sure we have something to store
    if not serial or not barcode:
        return
    # Define the query
    query = '''
        INSERT INTO [mes].[RawBarcodes] (
            [SerialNoNumber],
            [RawBarcode]
        )
        VALUES (
            ?,
            ?
        );
    '''
    # Define the arguments
    args = [serial, barcode]
    try:
        # Run the query
        system.db.runPrepUpdate(query, args, '' if tx else 'REVMES', tx)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())


def simple_print_label(printer_ip, part_number, rev, facility, serial):
    """
    Send data to a printer to perform a label print

    :params printer_ip: The IP address of the printer
    :params part_number: The Proterra part number
    :params rev: The Proterra part rev
    :params facility: The Proterra facility
    :params serial: The Proterra serial number
    """
    msg = ''

    # Determine the part number style
    part1 = part_number
    part2 = rev
    if len(part_number) > 8:
        # Split the part number into 2 halves to place on separate lines
        part1 = part_number[:8]
        part2 = part_number[8:]
    # Split the serial number part into 2 halves to place on separate lines
    serial1 = serial[:7]
    serial2 = serial[7:]

    # The template label code generated by Zebra designer
    template = """
        ~CD,~CC^~CT~
        ^XA
        ~TA000
        ~JSN
        ^MNW
        ^MTT
        ^PON
        ^PMN
        ^LH0,0
        ^JMA
        ^PR4,4
        ~SD22
        ^JUS
        ^LRN
        ^CI27
        ^PA0,1,1,0
        ^XZ
        ^XA
        ^MMT
        ^PW204
        ^LL102
        ^LS0
        ^FT106,26^A0N,17,18^FH\^CI28^FD++7PN++^FS^CI27
        ^FT106,47^A0N,17,18^FH\^CI28^FD++STYLE++^FS^CI27
        ^FT106,68^A0N,17,18^FH\^CI28^FD++FIRSTSN++^FS^CI27
        ^FT106,89^A0N,17,18^FH\^CI28^FD++SECSN++^FS^CI27
        ^FT25,80^BXN,3,200,0,0,1,_,1
        ^FH\^FD++PN++|++REV++|++FAC++|++SN++^FS
        ^PQ1,0,1,Y
        ^XZ
    """

    # Replace the placeholder data with real data
    data = template.replace('++7PN++', str(part1))
    data = data.replace('++STYLE++', str(part2))
    data = data.replace('++FIRSTSN++', str(serial1))
    data = data.replace('++SECSN++', str(serial2))
    data = data.replace('++PN++', str(part_number))
    data = data.replace('++REV++', str(rev))
    data = data.replace('++FAC++', str(facility))
    data = data.replace('++SN++', str(serial))

    # Debug log the data
    _logger.debug(data)

    # Send the data to the printer
    try:
        # Create the TCP socket and send the formatted data
        tcp_socket = socket(AF_INET, SOCK_STREAM)
        tcp_socket.connect((printer_ip, 9100))
        tcp_socket.sendall(data)
    except (Exception, JavaException):
        msg = 'Failed to send data to printer, see logs for exception details'
        _logger.error(traceback.format_exc())
    finally:
        # Close the socket
        tcp_socket.close()
    return msg


def print_label(printer_ip, base_tag_path, reason, asset, line, operator):
    """
    Print a Proterra barcode label

    :params printer_ip: The IP address of the printer
    :params base_tag_path: The base tag path of the barcode information
    :params reason: The reason for the print, Auto or Manual
    """
    barcode_path = system.tag.readBlocking('{}/Parameters.SerialNumberPath'.format(base_tag_path))[0].value
    complete_path = '{}/{}/Complete'.format(base_tag_path, reason)
    printing_path = '{}/{}/PrintingSerialNumber'.format(base_tag_path, reason)

    # Read the barcode from the tag
    barcode = system.tag.readBlocking(barcode_path)[0].value
    if not barcode:
        msg = 'Cannot parse barcode from path {}'.format(barcode_path)
        _logger.warn(msg)
        system.tag.writeBlocking(complete_path, 0)
        return

    # Parse the barcode parts
    part_number = __get_proterra_part_number(barcode)
    rev = __get_proterra_rev(barcode)
    facility = __get_proterra_facility(barcode)
    serial = __get_proterra_serial(barcode)
    if not part_number or not rev or not facility or not serial:
        msg = 'Cannot parse barcode parts from barcode {}'.format(barcode)
        _logger.warn(msg)
        system.tag.writeBlocking(complete_path, 0)
        return

    # Get the serial number model
    item = serial_number.get_serial_number_by_number(serial, False)
    # Ensure we found the item
    if item.ItemSerialNoId == -1:
        msg = 'Cannot find serial number {} in database'.format(serial)
        _logger.warn(msg)
        system.tag.writeBlocking(complete_path, 0)
        return

    # Send the data to the printer
    msg = simple_print_label(printer_ip, part_number, rev, facility, serial)
    if msg:
        _logger.warn(msg)
        system.tag.writeBlocking(complete_path, 0)
        return

    # Insert a print event for the serial number
    try:
        # Define the query
        query = '''
            INSERT INTO [MESDB].[mes].[Item_Events] (
                [SerialNumberId],
                [AssetId],
                [CreatedBy],
                [CreatedAt],
                [ItemReasonId])
            VALUES (
                ?,
                (SELECT TOP(1) AssetID
                FROM [MESDB].[mes].[Asset_Model]
                WHERE AssetName=?
                AND Line=?),
                ?,
                GETDATE(),
                (SELECT TOP(1) ItemReasonId
                FROM [MESDB].[product].[Item_Reason]
                WHERE ItemReasonName=?));
        '''
        # Define the arguments
        reason_name = 'Manual Barcode Label Print' if reason == 'Manual' else 'Automatic Barcode Label Print'
        args = [item.ItemSerialNoId, asset, line, operator, reason_name]
        # Run the query
        system.db.runPrepUpdate(query, args, 'MESDB')
    except (Exception, JavaException):
        msg = 'Failed to insert print event, see logs for exception details'
        _logger.error(traceback.format_exc())
        system.tag.writeBlocking(complete_path, 0)
        return
    # Write the barcode and set complete to 1
    system.tag.writeBlocking(printing_path, barcode)
    system.tag.writeBlocking(complete_path, 1)
