.ia_videoPlayerComponent {
    background-color: var(--container);
}

.ia_videoPlayerComponent__controlContainer {
    background-color: var(--container);
    border: var(--containerBorder);
    color: var(--neutral-70);
}

.ia_videoPlayerComponent__controlContainer__playControl,
.ia_videoPlayerComponent__controlContainer__volumeControl,
.ia_videoPlayerComponent__controlContainer__playRateControl,
.ia_videoPlayerComponent__controlContainer__fullscreenControl {
    fill: currentColor;
}

.ia_videoPlayerComponent__controlContainer__playControl:hover:not(.disabled),
.ia_videoPlayerComponent__controlContainer__volumeControl:hover:not(.disabled),
.ia_videoPlayerComponent__controlContainer__playRateControl:hover:not(.disabled),
.ia_videoPlayerComponent__controlContainer__fullscreenControl:hover:not(.disabled) {
    color: var(--icon--hover);
}

.ia_videoPlayerComponent__controlContainer__timeDurationDisplay {
    color: var(--label);
}

.ia_videoPlayerComponent__controlPopupContainer,
.ia_videoPlayerComponent__contextMenuPopupContainer {
    background-color: rgba(0, 0, 0, 1);
    color: var(--callToAction);
}

.ia_videoPlayerComponent__videoOverlay {
    background-color: transparent;
    color: var(--white);
}

.ia_videoPlayerComponent__videoOverlay--paused {
    background-color: var(--opacity-50);
}

.ia_videoPlayerComponent__videoOverlay_centralContainer {
    background-color: var(--opacity-85);
}
