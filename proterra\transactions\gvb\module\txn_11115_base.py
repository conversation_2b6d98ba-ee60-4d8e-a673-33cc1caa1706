import system.tag
import system.util
from proterra.barcodes import vendor as vendor_barcodes
from proterra.transactions import SerialNumber, TransactionBase, WorkOrder


class T11115Base(TransactionBase):
    def __init__(self, logger, facility, area, line, zone, number):
        crabb_name = 'CRABB1' if number == 11115 else 'CRABB2'
        tag_path_base = '[Proterra]Proterra/Greenville/GVB/Module/Line{}/Zone1/{}/CassetteBuild/CassetteSerialNumberValidation'\
            .format(line, crabb_name)
        user = '{}_L{}_{}_T{}'\
            .format(facility, line, crabb_name, number)
        super(T11115Base, self).__init__(logger, facility, area, line, zone, number, tag_path_base, user)

    def _execute(self):
        """
        Execute the transaction
        """
        tag_paths = [
            '{}/WorkOrderId'.format(self.tag_path_base),
            '{}/Inputs/AssetId'.format(self.tag_path_base),
            '{}/Parameters.rootTagPath'.format(self.tag_path_base)
        ]
        # Read the tag paths
        results = system.tag.readBlocking(tag_paths)
        # Check if results were returned
        if not results:
            raise ValueError('Failed to read tags from {}'.format(self.tag_path_base))
        if not results[0].getQuality().isGood():
            raise ValueError('Failed to read WorkOrderId from {}/WorkOrderId'.format(self.tag_path_base))
        if not results[1].getQuality().isGood():
            raise ValueError('Failed to read AssetId from {}/Inputs/AssetId'.format(self.tag_path_base))
        if not results[2].getQuality().isGood():
            raise ValueError('Failed to read rootTagPath from {}/Parameters.rootTagPath'.format(self.tag_path_base))
        # Get the work order ID
        work_order_id = int(results[0].getValue())
        if work_order_id < 1:
            raise ValueError('WorkOrderId is not valid: {}'.format(work_order_id))
        # Get the asset ID
        self.asset_id = int(results[1].getValue())
        if self.asset_id < 1:
            raise ValueError('AssetId is not valid: {}'.format(self.asset_id))
        # Get the root tag path
        root_tag_path = str(results[2].getValue())
        if not root_tag_path:
            raise ValueError('rootTagPath is empty for {}/Parameters.rootTagPath'.format(self.tag_path_base))

        tag_paths = [
            '{}/RobotInHand_SN_Proterra'.format(root_tag_path),
            '{}/RobotInHand_SN_Manufacturer'.format(root_tag_path),
        ]
        # Read the tag paths
        results = system.tag.readBlocking(tag_paths)
        # Check if results were returned
        if not results:
            raise ValueError('Failed to read tags from {}'.format(root_tag_path))
        if not results[0].getQuality().isGood():
            raise ValueError(
                'Failed to read RobotInHand_SN_Proterra from {}/RobotInHand_SN_Proterra'.format(root_tag_path))
        if not results[1].getQuality().isGood():
            raise ValueError(
                'Failed to read RobotInHand_SN_Manufacturer from {}/RobotInHand_SN_Manufacturer'.format(root_tag_path))
        # Get the Proterra serial number from the PLC
        plc_proterra_serial_number = str(results[0].getValue())
        if not plc_proterra_serial_number:
            raise ValueError('RobotInHand_SN_Proterra is empty for {}/RobotInHand_SN_Proterra'.format(root_tag_path))
        # Get the vendor barcode from the PLC
        plc_vendor_barcode = str(results[1].getValue())
        if not plc_vendor_barcode:
            raise ValueError(
                'RobotInHand_SN_Manufacturer is empty for {}/RobotInHand_SN_Manufacturer'.format(root_tag_path))
        # Get the vendor barcode parts
        plc_vendor_serial, _, _, plc_vendor_item_version_id, _ = vendor_barcodes.validate_cassette_sleeve_barcode(
            plc_vendor_barcode)
        if not plc_vendor_serial or plc_vendor_item_version_id == -1:
            raise ValueError('Invalid vendor barcode: {}'.format(plc_vendor_barcode))

        # Get the current work order information
        work_order = WorkOrder(work_order_id)
        # Check if the work order has run out of available serial numbers
        if work_order.available_count == 0:
            if work_order.next_work_order_id > 0:
                # If the current work order is full, try use the next valid work order to generate the serial number
                work_order = WorkOrder(work_order.next_work_order_id)
        # Check again if the work order has run out of available serial numbers
        if work_order.available_count == 0:
            raise ValueError('Work order {} has run out of available serial numbers'.format(work_order_id))
        # Produce the next Proterra serial number
        next_proterra_serial = SerialNumber.produce(
            work_order.item_version_id, self.asset_id, work_order.prefix, self.line, work_order.id, self.user)
        # Set the next Proterra serial state to IN USE
        next_proterra_serial.update_state('IN USE', self.user)

        # Check if the vendor serial number already exists
        vendor_serial = SerialNumber(plc_vendor_serial)
        if not vendor_serial.id:
            # Get the valid item version IDs for the 'Cassette Load' operation
            valid_operation_item_version_ids = work_order.get_valid_operation_item_version_ids('Cassette Load')
            if plc_vendor_item_version_id not in valid_operation_item_version_ids:
                # TODO: Enable the hard fail here. The ERPSYNC currently contains a bug that sets the expected item version id incorrectly
                self._logger.error('Item version id {} is not valid for operation: Cassette Load. Valid item version ids are: {}'
                                   .format(plc_vendor_item_version_id, ','.join(map(str, valid_operation_item_version_ids))))
                # TODO: Enable the hard fail here. The ERPSYNC currently contains a bug that sets the expected item version id incorrectly
                #raise ValueError('Item version id {} is not valid for operation: Cassette Load'.format(plc_vendor_item_version_id))
            # Create the vendor serial number
            vendor_serial = SerialNumber.create(plc_vendor_item_version_id, plc_vendor_serial, self.asset_id, self.user)
        if not vendor_serial.id:
            raise ValueError('Cannot create vendor serial number {}'.format(plc_vendor_serial))
        # Check if the Proterra serial number already exists
        proterra_serial = SerialNumber(plc_proterra_serial_number)
        if not proterra_serial.id:
            # Create the Proterra serial number
            proterra_serial = SerialNumber.create(work_order.item_version_id,
                                                  plc_proterra_serial_number, self.asset_id, self.user)
        if not proterra_serial.id:
            raise ValueError('Cannot create Proterra serial number {}'.format(plc_proterra_serial_number))

        # Consume the vendor serial number to the Proterra serial number
        proterra_serial.consume(work_order.id, vendor_serial, self.asset_id, self.user)
        # Set the vendor serial state to CONSUMED
        vendor_serial.update_state('CONSUMED', self.user)
        # Set the Proterra serial state to ENGRAVED
        proterra_serial.update_state('ENGRAVED', self.user)

        tag_paths = [
            '{}/SerialNumber'.format(self.tag_path_base)
        ]
        tag_values = [
            next_proterra_serial.serial_number
        ]
        # Write the tag paths
        system.tag.writeAsync(tag_paths, tag_values)
