#!/usr/bin/env python3
"""
ULTRASonic Welder Data Reader for RaspPi
Connects to VersaGraphics welder via TCP socket to capture weld data
Publishes data to MQTT broker with each data point as its own topic
and saves to CSV

Now with REST API for status and config changes for NodeBlack

Set Welder Config section for new welders on first use.

Version 0.9.0
Author: THWI


"""

import socket
import time
import logging
from datetime import datetime
import csv
import os
import json
import threading
from flask import Flask, request, jsonify
from smb.SMBConnection import SMBConnection
import paho.mqtt.client as mqtt

##### Welder Config - Set this for new welders #######
WELDER_IP = "**************"
WELDER_PORT = 4200
RECONNECT_DELAY = 5  # sec
LOG_FILE = "weld_data.csv"
SMB_SERVER = "v1-filesrv-02"
SMB_SHARE = "Manufacturing$"
SMB_FOLDER = "C1 Data Files"
SMB_USER = "ENTER SERVICE ACCOUNT HERE"
SMB_PASS = "ENTER SERVICE ACCOUNT PASSWORD HERE"
SMB_DOMAIN = "bus.local"

# MQTT Configuration
MQTT_BROKER = "vf-gateway-01"
MQTT_PORT = 1883
MQTT_USERNAME = "visualfactory"
MQTT_PASSWORD = "Pr0terr@"
MQTT_RESULT_TOPIC_SUFFIX = "result"

API_PORT = 8080

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("welder_reader.log"), logging.StreamHandler()],
)


class WelderDataReader:
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.csv_file = None
        self.csv_writer = None
        self.mqtt_client = None
        self.mqtt_connected = False

        # For welder name/topic config and most recent data
        self.config_file = "welder_config.json"
        self.last_published = None
        self.load_config()

        # Flask API
        self.flask_app = Flask(__name__)
        self.setup_flask_routes()
        self.api_thread = threading.Thread(target=self.run_api_server, daemon=True)
        self.api_thread.start()

        # MQTT setup
        self.setup_mqtt()

    def load_config(self):
        """Load welder name from config file, fallback to default"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r") as f:
                    config = json.load(f)
                    self.welder_name = config.get("welder_name", "USW1")
            else:
                self.welder_name = "USW1"
        except Exception as e:
            logging.warning(f"Failed to load config: {e}")
            self.welder_name = "USW1"
        self.set_mqtt_base_topic()

    def save_config(self):
        """Save welder name to config file"""
        try:
            with open(self.config_file, "w") as f:
                json.dump({"welder_name": self.welder_name}, f)
        except Exception as e:
            logging.warning(f"Failed to save config: {e}")

    def get_raspi_ip(self):
        """Get the Raspberry Pi's IP address"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception as e:
            logging.error(f"Failed to get Raspberry Pi's IP address: {e}")
            return "Unknown"

    def set_mqtt_base_topic(self):
        """Set base topic using welder name"""
        self.mqtt_base_topic = f"nomuda/gvl/tools/GVB-BRANSON/{self.welder_name}"

    def setup_flask_routes(self):
        @self.flask_app.route("/status", methods=["GET"])
        def get_status():
            if self.last_published:
                return jsonify({"ok": True, "last": self.last_published})
            return jsonify({"ok": False, "msg": "No data published yet"})

        @self.flask_app.route("/set_name", methods=["POST"])
        def set_name():
            data = request.get_json(force=True)
            new_name = data.get("name")
            if new_name:
                logging.info(f"API: Changing welder name to {new_name}")
                self.welder_name = new_name
                self.set_mqtt_base_topic()
                self.save_config()
                return jsonify({"ok": True, "msg": f"Welder name set to {new_name}"})
            else:
                return jsonify({"ok": False, "msg": "No name provided"}), 400

        @self.flask_app.route("/info", methods=["GET"])
        def info():
            return jsonify(
                {
                    "welder_name": self.welder_name,
                    "mqtt_base_topic": self.mqtt_base_topic,
                    "ip": self.host,
                    "connected": self.connected,
                }
            )

        @self.flask_app.route("/retry_mqtt", methods=["POST"])
        def retry_mqtt():
            if self.last_published:
                logging.info("API: Re-publishing last data to MQTT on demand.")
                self.publish_to_mqtt(self.last_published, retry_on_fail=True)
                return jsonify(
                    {
                        "ok": True,
                        "msg": "Retried MQTT publish",
                        "last": self.last_published,
                    }
                )
            else:
                return jsonify({"ok": False, "msg": "No previous data to publish"}), 400

    def run_api_server(self):
        self.flask_app.run(host="0.0.0.0", port=API_PORT)

    def setup_mqtt(self):
        """Initialize MQTT client"""
        try:
            self.mqtt_client = mqtt.Client()
            self.mqtt_client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)

            def on_connect(client, userdata, flags, rc):
                if rc == 0:
                    self.mqtt_connected = True
                    logging.info("Connected to MQTT broker")
                else:
                    self.mqtt_connected = False
                    logging.error(f"Failed to connect to MQTT broker, return code {rc}")

            def on_disconnect(client, userdata, rc):
                self.mqtt_connected = False
                logging.warning("Disconnected from MQTT broker")

            def on_publish(client, userdata, mid):
                logging.debug(f"MQTT message published: {mid}")

            self.mqtt_client.on_connect = on_connect
            self.mqtt_client.on_disconnect = on_disconnect
            self.mqtt_client.on_publish = on_publish

            self.mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.mqtt_client.loop_start()
        except Exception as e:
            logging.error(f"Error setting up MQTT: {e}")
            self.mqtt_connected = False

    def connect(self):
        """Connect to the welder's TCP socket"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.host, self.port))
            self.connected = True
            logging.info(f"Connected to welder at {self.host}:{self.port}")
            return True
        except Exception as e:
            logging.error(f"Failed to connect: {e}")
            self.connected = False
            return False

    def disconnect(self):
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.connected = False
        logging.info("Disconnected from welder")

    def setup_csv_file(self):
        file_exists = os.path.exists(LOG_FILE)
        self.csv_file = open(LOG_FILE, "a", newline="")
        self.csv_writer = csv.writer(self.csv_file)
        if not file_exists:
            headers = [
                "Timestamp",
                "Cycle_Count",
                "Date",
                "Time",
                "Part_Name",
                "Process_Parameters",
                "Energy",
                "Trigger_Pressure",
                "Pressure",
                "Amplitude",
                "Quality_Windows",
                "Time_Plus",
                "Time_Minus",
                "Power_Plus",
                "Power_Minus",
                "PreHeight_Plus",
                "PreHeight_Minus",
                "Height_Plus",
                "Height_Minus",
                "Weld_Results",
                "Weld_Time",
                "Weld_Power",
                "Pre_Height",
                "Height",
                "Alarms",
                "Graph_Data_Ratio",
                "Graph_Data",
            ]
            self.csv_writer.writerow(headers)
            self.csv_file.flush()
            logging.info("Created new CSV file with headers")

    def parse_weld_data(self, data_line):
        try:
            parts = data_line.strip().split("\t")
            if len(parts) == 1:
                import re

                parts = re.split(r"\s{2,}", data_line.strip())
            if len(parts) >= 26:
                parsed_data = {
                    "timestamp": datetime.now().isoformat(),
                    "cycle_count": parts[0].strip(),
                    "date": parts[1].strip(),
                    "time": parts[2].strip(),
                    "part_name": parts[3].strip(),
                    "process_parameters": parts[4].strip(),
                    "energy": parts[5].strip(),
                    "trigger_pressure": parts[6].strip(),
                    "pressure": parts[7].strip(),
                    "amplitude": parts[8].strip(),
                    "quality_windows": parts[9].strip(),
                    "time_plus": parts[10].strip(),
                    "time_minus": parts[11].strip(),
                    "power_plus": parts[12].strip(),
                    "power_minus": parts[13].strip(),
                    "preheight_plus": parts[14].strip(),
                    "preheight_minus": parts[15].strip(),
                    "height_plus": parts[16].strip(),
                    "height_minus": parts[17].strip(),
                    "weld_results": parts[18].strip(),
                    "weld_time": parts[19].strip(),
                    "weld_power": parts[20].strip(),
                    "pre_height": parts[21].strip(),
                    "height": parts[22].strip(),
                    "alarms": parts[23].strip(),
                    "graph_data_ratio": parts[24].strip(),
                    "graph_data": (parts[25].strip() if len(parts) > 25 else ""),
                }
                return parsed_data
            else:
                logging.warning(
                    f"Line has fewer parts ({len(parts)}) than expected (26): {data_line}"
                )
        except Exception as e:
            logging.error(f"Error parsing weld data: {e}")
            logging.error(f"Data line was: {data_line}")
        return None

    def publish_to_mqtt(self, data, retry_on_fail=True):
        if not data:
            return

        if not self.mqtt_connected and retry_on_fail:
            logging.warning(
                "MQTT is not connected. Attempting to reconnect before publishing..."
            )
            try:
                self.mqtt_client.reconnect()
                time.sleep(1)
            except Exception as e:
                logging.error(f"MQTT reconnect attempt failed: {e}")

        if self.mqtt_connected:
            base_topic_for_results = (
                f"{self.mqtt_base_topic}/{MQTT_RESULT_TOPIC_SUFFIX}"
            )
            for key, value in data.items():
                topic = f"{base_topic_for_results}/{key}"
                payload = str(value)
                self.mqtt_client.publish(topic, payload, qos=1)
                logging.debug(f"Published to MQTT: {topic} -> {payload}")
            logging.info(
                f"Published all data points for a weld cycle to {base_topic_for_results}/..."
            )
            self.last_published = data
        else:
            logging.warning("Still not connected to MQTT broker. Data not published.")

    def log_weld_data(self, data):
        if self.csv_writer and data:
            row = [
                data.get("timestamp", ""),
                data.get("cycle_count", ""),
                data.get("date", ""),
                data.get("time", ""),
                data.get("part_name", ""),
                data.get("process_parameters", ""),
                data.get("energy", ""),
                data.get("trigger_pressure", ""),
                data.get("pressure", ""),
                data.get("amplitude", ""),
                data.get("quality_windows", ""),
                data.get("time_plus", ""),
                data.get("time_minus", ""),
                data.get("power_plus", ""),
                data.get("power_minus", ""),
                data.get("preheight_plus", ""),
                data.get("preheight_minus", ""),
                data.get("height_plus", ""),
                data.get("height_minus", ""),
                data.get("weld_results", ""),
                data.get("weld_time", ""),
                data.get("weld_power", ""),
                data.get("pre_height", ""),
                data.get("height", ""),
                data.get("alarms", ""),
                data.get("graph_data_ratio", ""),
                data.get("graph_data", ""),
            ]
            self.csv_writer.writerow(row)
            self.csv_file.flush()
            logging.info(
                f"Logged weld data to CSV: {data.get('part_name', 'N/A')} - Cycle: {data.get('cycle_count', 'N/A')}"
            )
        self.publish_to_mqtt(data)

    def upload_csv_to_smb(local_file, welder_name):
        """attempt to upload the CSV file to an SMB share"""
        raspi_ip = get_raspi_ip()
        remote_filename = f"{welder_name}-{raspi_ip}.csv"
        remote_path = f"{SMB_FOLDER}/{remote_filename}"

        try:
            conn = SMBConnection(
                SMB_USER,
                SMB_PASS,
                "raspberrypi",
                SMB_SERVER,
                domain=SMB_DOMAIN,
                use_ntlm_v2=True,
                is_direct_tcp=True,
            )
            assert conn.connect(SMB_SERVER, 445), "SMB connection failed"
            with open(local_file, "rb") as f:
                conn.storeFile(SMB_SHARE, remote_path, f)
            print(f"Uploaded {local_file} to //{SMB_SERVER}/{SMB_SHARE}/{remote_path}")
        except Exception as e:
            print(f"Error uploading CSV to SMB: {e}")

    def is_header_line(self, line):
        header_indicators = [
            "Versagraphix Daily log",
            "Daily log",
            "AmTech Daily log",
            "Cycle count",
            "Date",
            "Time",
            "Part Name",
            "Process Parameters",
            "Energy",
            "Trigger Pressure",
            "Quality Windows",
        ]
        line_lower = line.lower()
        return any(indicator.lower() in line_lower for indicator in header_indicators)

    def read_data(self):
        buffer = ""
        while self.connected:
            try:
                data = self.socket.recv(1024).decode("utf-8", errors="ignore")
                if not data:
                    logging.warning("No data received - connection may be closed")
                    break
                buffer += data
                while "\n" in buffer:
                    line, buffer = buffer.split("\n", 1)
                    line = line.strip()
                    if line:
                        logging.info(f"Received: {line}")
                        if self.is_header_line(line):
                            logging.info("Received header line - skipping")
                            continue
                        if len(line.strip()) > 10 and not line.strip().startswith(
                            "Received:"
                        ):
                            parsed_data = self.parse_weld_data(line)
                            if parsed_data and parsed_data.get("part_name"):
                                self.log_weld_data(parsed_data)
                                try:
                                    self.upload_csv_to_smb(
                                        self.csv_file.name, self.welder_name
                                    )
                                except Exception as e:
                                    logging.error(f"Error uploading CSV to SMB: {e}")
                                print(
                                    f"Weld completed: {parsed_data['part_name']} - Cycle: {parsed_data['cycle_count']}"
                                )
                            else:
                                logging.debug(
                                    f"Could not parse or recognize data line: {line}"
                                )
            except socket.timeout:
                continue
            except Exception as e:
                logging.error(f"Error reading data: {e}")
                break

    def run(self):
        self.setup_csv_file()
        try:
            while True:
                if not self.connected:
                    logging.info("Attempting to connect...")
                    if self.connect():
                        logging.info(
                            "Connected successfully - listening for weld data..."
                        )
                        self.read_data()
                    else:
                        logging.info(
                            f"Connection failed, retrying in {RECONNECT_DELAY} seconds..."
                        )
                        time.sleep(RECONNECT_DELAY)
                else:
                    self.read_data()
                self.disconnect()
                logging.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                time.sleep(RECONNECT_DELAY)
        except KeyboardInterrupt:
            logging.info("Shutting down...")
        finally:
            self.disconnect()
            if self.csv_file:
                self.csv_file.close()
            if self.mqtt_client:
                self.mqtt_client.loop_stop()
                self.mqtt_client.disconnect()


def main():
    print("Sonic Welder Data Reader Starting...")
    print(f"Connecting to welder at {WELDER_IP}:{WELDER_PORT}")
    print(f"MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
    print("API Server: http://0.0.0.0:8080/")
    reader = WelderDataReader(WELDER_IP, WELDER_PORT)
    reader.run()


if __name__ == "__main__":
    main()
