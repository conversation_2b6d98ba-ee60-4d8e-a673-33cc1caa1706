import platform
from proterra.bmb import rework
from proterra.models.item_version import Item_Version
from proterra.models.serial_number import Item_SerialNo
from proterra.testing.mock_classes import MockTimestamp
from datetime import datetime
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner
from mock import patch, MagicMock

item_version = Item_Version()
item_version.ItemVersionId = 1
item_version.ItemId = 1
item_version.ItemNo = '199-4631'
item_version.ItemDescription = 'Test Item'
item_version.ProductionVersion = 'A01'
item_version.CreatedBy = 'ReworkTests'
item_version.CreatedAt = MockTimestamp(datetime.now().microsecond / 1000)
item_version.EditedBy = 'ReworkTests'
item_version.EditedAt = MockTimestamp(datetime.now().microsecond / 1000)

serial_number1 = Item_SerialNo()
serial_number1.ItemSerialNoId = 1
serial_number1.ItemVersionId = item_version.ItemVersionId
serial_number1.SerialNoNumber = 'M421421'
serial_number1.ItemSerialNoStateId = 1
serial_number1.AssetId = 1
serial_number1.CreatedBy = 'ReworkTests'
serial_number1.CreatedAt = MockTimestamp(datetime.now().microsecond / 1000)
serial_number1.EditedBy = 'ReworkTests'
serial_number1.EditedAt = MockTimestamp(datetime.now().microsecond / 1000)

serial_number2 = Item_SerialNo()
serial_number2.ItemSerialNoId = 1
serial_number2.ItemVersionId = item_version.ItemVersionId
serial_number2.SerialNoNumber = 'S421422'
serial_number2.ItemSerialNoStateId = 1
serial_number2.AssetId = 1
serial_number2.CreatedBy = 'ReworkTests'
serial_number2.CreatedAt = MockTimestamp(datetime.now().microsecond / 1000)
serial_number2.EditedBy = 'ReworkTests'
serial_number2.EditedAt = MockTimestamp(datetime.now().microsecond / 1000)

@patch('system.db.runScalarPrepQuery')
@patch('system.db.runPrepQuery')
@patch('system.db.createSProcCall')
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
@patch('proterra.models.item_version.get_item_version_by_id', MagicMock(return_value=item_version))
@patch('proterra.models.item_version.get_item_version_by_no', MagicMock(return_value=item_version))
@patch('proterra.models.serial_number.insert_serial_number', MagicMock(side_effect=[serial_number1, serial_number2]))
@patch('proterra.models.serial_number.generate_serial_number', MagicMock(side_effect=[serial_number1, serial_number2]))
class ReworkTests(TestCase):
    def setUp(self):
        self.test_module_in = [{
            'ItemSerialNoId': 420421,
            'ItemVersionId': 1,
            'SerialNoNumber': 'M420421',
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'ReworkTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'ReworkTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000)
        }]
        self.test_spine_in = [{
            'ItemSerialNoId': 420422,
            'ItemVersionId': 1,
            'SerialNoNumber': 'S420422',
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'ReworkTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'ReworkTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000)
        }]
        self.test_bmb_in = [{
            'ItemSerialNoId': 420423,
            'ItemVersionId': 1,
            'SerialNoNumber': '000019839722426000139201420423111E03',
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'ReworkTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'ReworkTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000)
        }]
        self.test_module_out = [{
            'ItemSerialNoId': 421421,
            'ItemVersionId': 1,
            'SerialNoNumber': 'M421421',
            'ItemSerialNoStateId': 1,
            'AssetId': 420,
            'CreatedBy': 'ReworkTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'ReworkTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'Result': 'Success',
            'ResultId': 421421,
            'ResultMessage': ''
        }]
        self.test_spine_out = [{
            'ItemSerialNoId': 421422,
            'ItemVersionId': 1,
            'SerialNoNumber': 'S421422',
            'ItemSerialNoStateId': 1,
            'AssetId': 420,
            'CreatedBy': 'ReworkTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'ReworkTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'Result': 'Success',
            'ResultId': 421422,
            'ResultMessage': ''
        }]
        self.test_bmb_out = [{
            'ItemSerialNoId': 421423,
            'ItemVersionId': 1,
            'SerialNoNumber': '000019839722426000139201421423111E03',
            'ItemSerialNoStateId': 1,
            'AssetId': 420,
            'CreatedBy': 'ReworkTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'ReworkTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'Result': 'Success',
            'ResultId': 421423,
            'ResultMessage': ''
        }]

    def test_perform_rework(self, createSProcCall, runPrepQuery, runScalarPrepQuery):
        createSProcCall.return_value.getResultSet = MagicMock(side_effect=[
            self.test_bmb_out, self.test_module_out, self.test_spine_out])
        createSProcCall.return_value.getOutParamValue = MagicMock(side_effect=[
            self.test_module_out[0]['SerialNoNumber'], self.test_spine_out[0]['SerialNoNumber']])
        runPrepQuery.side_effect = [
            self.test_module_in, self.test_spine_in, self.test_bmb_in, [{'AssetID': 420, 'Line': 9}]]
        runScalarPrepQuery.side_effect = [self.test_module_in[0]['ItemSerialNoId']]
        new_module_serial_number, new_spine_serial_number, msg = rework.perform_rework(
            'M420421', 'S420422', '000019839722426000139201421423111E03', 1, 1, 'ReworkTests')
        self.assertEqual('', msg)
        self.assertEqual('M421421', new_module_serial_number)
        self.assertEqual('S421422', new_spine_serial_number)

    def test_perform_rework_exception(self, createSProcCall, runPrepQuery, runScalarPrepQuery):
        runScalarPrepQuery.side_effect = Exception('ReworkTests')
        new_module_serial_number, new_spine_serial_number, msg = rework.perform_rework(
            'M420421', 'S420422', '000019839722426000139201421423111E03', 1, 1, 'ReworkTests')
        self.assertNotEqual('', msg)
        self.assertEqual('', new_module_serial_number)
        self.assertEqual('', new_spine_serial_number)


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            ReworkTests,
            ReworkTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
