import platform
from proterra.barcodes import vendor as vendor_barcodes
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner
from mock import patch, MagicMock


@patch('system.db.runPrepQuery')
@patch('system.db.runPrepUpdate', MagicMock())
class CassetteSleeveBarcodeTests(TestCase):
    def setUp(self):
        self.test_dataset = [{
            'ItemVersionId': 1,
            'ItemNo': '194-2464',
            'ProductionVersion': 'D01'
        }]

    def test1(self, runPrepQuery):
        runPrepQuery.return_value = self.test_dataset
        barcode = '1599052550C0212312023K23L0640001300149347'
        serial, part, rev, item_version_id, search = vendor_barcodes.validate_cassette_sleeve_barcode(barcode)
        self.assertIsNotNone(serial)
        self.assertIsNotNone(part)
        self.assertIsNotNone(rev)
        self.assertTrue(item_version_id>0)
        self.assertIsNotNone(search)

    def test2(self, runPrepQuery):
        runPrepQuery.return_value = self.test_dataset
        barcode = '1599|194-2464|D01|PA/PPEGF10|20241028|1300001108|1|01'
        serial, part, rev, item_version_id, search = vendor_barcodes.validate_cassette_sleeve_barcode(barcode)
        self.assertIsNotNone(serial)
        self.assertIsNotNone(part)
        self.assertIsNotNone(rev)
        self.assertTrue(item_version_id>0)
        self.assertIsNotNone(search)


class FoilBarcodeTests(TestCase):
    pass


class SpineBarcodeTests(TestCase):
    pass


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            CassetteSleeveBarcodeTests,
            FoilBarcodeTests,
            SpineBarcodeTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
