.ia_grid__cell {
    background-color: var(--container);
}

.ia_dashboardComponent {
    background-color: var(--containerRoot);
    border: var(--containerBorder);
}

.ia_dashboardComponent__control {
    background-color: var(--opacity-25);
    color: var(--neutral-20);
}

.ia_dashboardComponent__control:hover {
    background-color: var(--opacity-50);
    color: var(--neutral-10);
}

.ia_dashboardComponent__cell__addWidget {
    color: var(--callToAction);
}

.ia_dashboardComponent__cell__addWidget:hover {
    color: var(--callToAction--hover);
}

.ia_dashboardComponent__addWidgetOverlay {
    background-color: var(--neutral-90);
    opacity: 0.2;
}

.ia_dashboardComponent__cell {
    background-color: var(--containerNested);
}

.ia_dashboardComponent__cell--active {
    background-color: var(--neutral-40);
}

.ia_dashboardComponent__movingWidgetPlaceholder {
    border: 2px dashed var(--border);
    border-radius: var(--borderRadius);
}

.ia_dashboardComponent__widget {
    border: var(--containerBorder);
    border-radius: var(--borderRadius);
}

.ia_dashboardComponent__widget--editing {
    border: 2px dashed var(--callToAction);
}

.ia_dashboardComponent__widget--configuring {
    border: 2px dashed var(--warning);
}

.ia_dashboardComponent__widget--moving {
    box-shadow: var(--boxShadow5);
}

.ia_dashboardComponent__widget__resizeHandle__handle {
    border-radius: 50%;
    border: 2px solid var(--callToAction);
    background-color: var(--neutral-10);
}

.ia_dashboardComponent__widget__resizeHandle__handle--configuring {
    border: 2px solid var(--warning);
}

.ia_dashboardComponent__widget__controlBar {
    color: var(--icon);
}

.ia_dashboardComponent__widget__controlBar__control--active {
    color: var(--warning);
}

.ia_dashboardComponent__widget__controlBar__control:hover {
    color: var(--icon--hover);
}

.ia_dashboardComponent__widget__head {
    background-color: var(--containerNested);
    border-bottom: var(--containerBorder);
    font-size: 0.875rem;
}

.ia_dashboardComponent__widget__head__title {
    text-align: left;
    padding: 0px 0.625rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ia_dashboardComponent__widget__body {
    background-color: var(--container);
}

.ia_dashboardComponent__addWidgetModal__head, .ia_dashboardComponent__removeWidgetModal__head {
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 0.875rem;
    background-color: var(--neutral-30);
    border-bottom: var(--containerBorder);
    color: var(--neutral-90);
}

.ia_dashboardComponent__addWidgetModal__searchContainer {
    background-color: var(--neutral-10);
    border-bottom: var(--containerBorder);
}

.ia_dashboardComponent__addWidgetModal__menu__category__titleBar {
    background-color: var(--neutral-30);
    border-bottom: var(--containerBorder);
    color: var(--neutral-90);
    font-size: 0.75rem;
}

.ia_dashboardComponent__addWidgetModal__body, .ia_dashboardComponent__removeWidgetModal__body {
    background-color: var(--neutral-10);
    color: var(--neutral-90);
    font-size: 0.875rem;
    line-height: 0.875rem;
    font-weight: 400;
}

.ia_dashboardComponent__addWidgetModal__menu__category__item {
    color: var(--neutral-90);
    border-bottom: var(--containerBorder);
    font-size: 0.875rem;
    line-height: 1rem;
    font-weight: 400;
}

.ia_dashboardComponent__addWidgetModal__menu__category__item:hover:not(.ia_dashboardComponent__addWidgetModal__menu__category__item--selected) {
    background-color: var(--callToActionHighlight);
}

.ia_dashboardComponent__addWidgetModal__menu__category__item--selected {
    background-color: var(--callToAction--active);
    color: var(--neutral-10);
}

.ia_dashboardComponent__addWidgetModal__foot {
    background-color: var(--neutral-30);
    border-top: var(--containerBorder);
}
