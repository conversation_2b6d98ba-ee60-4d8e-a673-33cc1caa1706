.ia_progressBar__track {
    border: var(--containerBorder);
    border-radius: 24px;
}

.ia_progressBar__track--determinate {
    background-color: var(--progressLinearTrack--determinate);
}

.ia_progressBar__track--indeterminate {
    background-color: var(--progressLinearTrack--indeterminate);
}

.ia_progressBar__bar {
    background-color: var(--progressLinearBar--determinate);
    transition: transform 0.2s linear;
}

.ia_progressBar__bar--determinate {
    transition: transform 0.2s linear;
}

.ia_progressBar__bar--indeterminate {
    animation: ia_progressBar_bar--indeterminateAnimation 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
    animation-name: ia_progressBar_bar--indeterminateAnimation;
}

.ia_progressBar__displayValue {
    color: var(--neutral-90);
}

@keyframes ia_progressBar_bar--indeterminateAnimation {
    0% {
        left: -35%;
        right: 100%;
    }

    100% {
        left: 100%;
        right: -90%;
    }
}
