.ia_menuTreeComponent__item {
    border-bottom: solid var(--border) 1px;
    background-color: inherit;
    color: var(--neutral-90);
}

.ia_menuTreeComponent__item:not(.ia_menuTreeComponent__header):hover {
    filter: brightness(0.9);
}

.ia_menuTreeComponent__item__icon,
.ia_menuTreeComponent__header__icon {
    fill: var(--icon);
}

.ia_menuTreeComponent__item__navIcon {
    fill: var(--icon);
}

.ia_menuTreeComponent__backAction {
    background-color: var(--neutral-30);
    color: var(--icon);
    border-bottom: solid var(--border) 1px;
    text-transform: uppercase;
    font-size: 0.875rem;
    line-height: 2rem;
    font-weight: 500;
}

.ia_menuTreeComponent__backAction:hover {
    background-color: var(--neutral-20);
}

.ia_menuTreeComponent__backAction:active {
    background-color: var(--neutral-40);
}
