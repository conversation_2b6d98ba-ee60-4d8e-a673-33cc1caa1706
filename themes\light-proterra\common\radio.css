.ia_radio__selectedIcon,
.ia_radio__unselectedIcon {
    transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    height: 21.65px;
    width: 21.65px;
}

.ia_radio__icon--useColor {
    fill: currentColor;
}

.ia_radio .ia_radio__selectedIcon {
    color: var(--radio--selected);
}

.ia_radio .ia_radio__unselectedIcon {
    color: var(--radio--unselected);
}

/* Disabled radio style */
.ia_radio .ia_radio__unselectedIcon--disabled,
.ia_radio .ia_radio__selectedIcon--disabled {
    color: var(--radio--disabled);
}

/* Add hover styles for enabled and selected */
.ia_radio:hover input:enabled~.ia_radio__selectedIcon {
    filter: brightness(1.2);
}

/* Add hover styles for enabled and unselected */
.ia_radio:hover input:enabled~.ia_radio__unselectedIcon {
    filter: brightness(0.5);
}

/* Add hover style for enabled */
.ia_radio input:enabled:active~.ia_radio__selectedIcon,
.ia_radio input:enabled:active~.ia_radio__unselectedIcon {
    filter: brightness(0.7);
}
