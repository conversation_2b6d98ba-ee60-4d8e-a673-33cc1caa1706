.ia_button--primary,
.ia_button--secondary {
    font-size: 0.875rem;
    font-weight: 700;
    border-radius: var(--borderRadius);
    -webkit-border-radius: var(--borderRadius);
    cursor: pointer;
    -webkit-transition-duration: 0.1s;
    transition-duration: 0.1s;
    transition-property: box-shadow, background-color;
}

.ia_button--primary:enabled:hover,
.ia_button--secondary:enabled:hover {
    box-shadow: var(--boxShadow2);
}

/* Primary button */

.ia_button--primary {
    background-color: var(--callToAction);
    border: var(--containerBorder);
    color: var(--neutral-10);
}

.ia_button--primary svg,
.ia_button--secondary svg {
    fill: currentColor;
}

.ia_button--primary:enabled:active {
    box-shadow: var(--boxShadow--inset);
    border: none;
}

.ia_button--primary--disabled {
    background-color: var(--callToAction--disabled);
    border: var(--border--disabled);
    color: var(--neutral-30);
    cursor: not-allowed;
}

/* Secondary button */

.ia_button--secondary {
    background-color: var(--neutral-10);
    border: var(--containerBorder);
    color: var(--neutral-90);
}

.ia_button--secondary:enabled:active {
    box-shadow: var(--boxShadow--inset);
}

.ia_button--secondary--disabled {
    background-color: var(--border--disabled);
    border: 1px solid var(--border--disabled);
    color: var(--neutral-30);
    cursor: not-allowed;
}
