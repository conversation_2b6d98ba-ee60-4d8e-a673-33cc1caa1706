.ia_numericEntryFieldComponent__modal {
    border: var(--containerBorder);
}

.ia_numericEntryFieldComponent__editIcon {
    fill: var(--callToAction);
}

.ia_numericEntryFieldComponent__editLink:hover .ia_numericEntryFieldComponent__editIcon {
    filter: brightness(1.2);
}

.ia_numericEntryFieldComponent__editLink--active:hover .ia_numericEntryFieldComponent__editIcon,
.ia_numericEntryFieldComponent__editLink--disabled:hover .ia_numericEntryFieldComponent__editIcon {
    filter: none;
}

.ia_numericEntryFieldComponent__editIcon--active {
    fill: var(--callToAction--active);
}

.ia_numericEntryFieldComponent__editIcon--disabled {
    fill: var(--callToAction--disabled);
}
