import platform
from proterra.barcodes import proterra as proterra_barcodes
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner
from mock import patch


@patch('system.db.runPrepQuery')
class ProterraBarcodeTests(TestCase):
    def setUp(self):
        self.test_dataset = [{
            'ItemVersionId': 1,
            'ItemNo': '199-4631',
            'ProductionVersion': 'A01'
        }]

    def test1(self, runPrepQuery):
        runPrepQuery.return_value = self.test_dataset
        barcode = '199-4631 | A01 | GVB | M2432720000189'
        serial, part, rev, fac, item_version_id, search = proterra_barcodes.validate_proterra_barcode(barcode)
        self.assertIsNotNone(serial)
        self.assertIsNotNone(part)
        self.assertIsNotNone(rev)
        self.assertIsNotNone(fac)
        self.assertTrue(item_version_id>0)
        self.assertIsNotNone(search)


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            ProterraBarcodeTests,
            ProterraBarcodeTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
