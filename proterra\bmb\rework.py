from java.lang import Exception as JavaException
from proterra.models import barcode, item_version, serial_number
from proterra.models.item_version import Item_Version
import traceback
import system.db
import system.util

_logger = system.util.getLogger('proterra.bmb.rework')


def perform_rework(old_module_serial, old_spine_serial, new_bmb_serial, new_module_iv_id, new_spine_iv_id, operator):
    """
    Perform a BMB rework

    :param old_module_serial: The old module serial number
    :param old_spine_serial: The old spine serial number
    :param old_bmb_serial: The old bmb serial number
    :param new_bmb_serial: The new bmb serial number
    :param operator: The operator performing the rework operation
    :returns: The new module and spine serial numbers, and an error string on error
    """
    new_module_iv = Item_Version()
    new_spine_iv = Item_Version()
    new_bmb_iv = Item_Version()
    try:
        # Get the new module, spine, and BMB item version information
        new_module_iv = item_version.get_item_version_by_id(new_module_iv_id)
        new_spine_iv = item_version.get_item_version_by_id(new_spine_iv_id)
        new_bmb_part_no = barcode.get_part_number(new_bmb_serial)
        if not new_bmb_part_no:
            return '', '', 'Cannot parse new BMB part number'
        new_bmb_iv = item_version.get_item_version_by_no(new_bmb_part_no)
    except (Exception, JavaException):
        msg = traceback.format_exc()
        _logger.error(msg)
        return '', '', msg

    # Make sure we got the item version information
    if new_module_iv.ItemVersionId == -1:
        return '', '', 'Cannot lookup module item version {} information'.format(new_module_iv_id)
    elif new_spine_iv.ItemVersionId == -1:
        return '', '', 'Cannot lookup spine item version {} information'.format(new_spine_iv_id)
    elif new_bmb_iv.ItemVersionId == -1:
        return '', '', 'Cannot lookup BMB part number {} item version information'.format(new_bmb_part_no)

    new_module_sn = ''
    new_spine_sn = ''
    try:
        # Get the old items
        old_module, old_spine, old_bmb = __get_old_items(old_module_serial, old_spine_serial)
        # Generate the new items
        new_module_sn, new_spine_sn = __generate_new_items(old_module, old_spine, old_bmb,
                                                           new_module_iv, new_spine_iv, new_bmb_iv,
                                                           new_bmb_serial, operator)
    except (Exception, JavaException):
        msg = traceback.format_exc()
        _logger.error(msg)
        return '', '', msg
    return new_module_sn, new_spine_sn, ''


def __get_old_items(old_module_serial, old_spine_serial):
    """
    Get the old module, spine, and BMB

    :param old_module_serial: The old module serial number
    :param old_spine_serial: The old spine serial number
    :returns: The old module, spine, and BMB models
    """
    # Get the old module serial number
    old_module = serial_number.get_serial_number_by_number(old_module_serial)
    # Ensure we found the old module
    if old_module.ItemSerialNoId == -1:
        raise Exception('Cannot look up old module serial number')
    # Get the old spine serial number
    old_spine = serial_number.get_serial_number_by_number(old_spine_serial)
    # Ensure we found the old spine
    if old_spine.ItemSerialNoId == -1:
        raise Exception('Cannot look up old spine serial number')
    # Get the old BMB serial number
    old_bmb = __get_consumed_bmb(old_spine)
    # Ensure we found the old BMB
    if old_bmb.ItemSerialNoId == -1:
        raise Exception('Cannot look up old BMB serial number')
    return old_module, old_spine, old_bmb


def __get_consumed_bmb(old_spine):
    """
    Get the BMB that was consumed into the old spine

    :param old_spine: The old spine
    :returns: The model for the BMB that was consumed into the spine
    """
    old_bmb_id = -1
    try:
        # Define the query
        query = '''
            SELECT TOP(1) isn.ItemSerialNoId
            FROM [MESDB].[mes].[Item_Consumption] ic
            JOIN [MESDB].[mes].[Item_SerialNo] isn
            ON ic.FromSerialNoId=isn.ItemSerialNoId
            WHERE ic.ToSerialNoId=?
            AND isn.SerialNoNumber LIKE '0000%';
        '''
        # Define the arguments
        args = [old_spine.ItemSerialNoId]
        # Run the query
        result = system.db.runScalarPrepQuery(query, args, 'MESDB_Replica')
        # Get the result
        old_bmb_id = int(result) if result else -1
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    # Ensure we found the old BMB
    if old_bmb_id == -1:
        raise Exception('Cannot look up old BMB serial number')
    # Get the old BMB serial number
    old_bmb = serial_number.get_serial_number_by_id(old_bmb_id)
    # Ensure we found the old BMB
    if old_bmb.ItemSerialNoId == -1:
        raise Exception('Cannot look up old BMB serial number')
    return old_bmb


def __get_rework_asset_info():
    """
    Get the rework asset information

    :returns: The asset id and line for the rework
    """
    asset_id = -1
    line = -1
    try:
        # Define the query
        query = '''
            SELECT TOP(1) [AssetID],[Line]
            FROM [MESDB].[mes].[Asset_Model]
            WHERE AssetName LIKE '%BMB%Rework%';
        '''
        # Define the arguments
        args = []
        # Run the query
        results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
        # Get the results
        if results:
            ret_asset_id = results[0]['AssetID']
            ret_line = results[0]['Line']
            # Convert the return values
            asset_id = int(ret_asset_id) if ret_asset_id else -1
            line = int(ret_line) if ret_line else -1
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return asset_id, line


def __generate_new_items(old_module, old_spine, old_bmb,
                         new_module_iv, new_spine_iv, new_bmb_iv,
                         new_bmb_serial, operator):
    """
    Generate the new module and spine, and insert the new BMB

    :param old_module: The old module
    :param old_spine: The old spine
    :param old_bmb: The old BMB
    :param new_module_iv: The new module item version information
    :param new_spine_iv: The new spine item version information
    :param new_bmb_iv: The new BMB item version information
    :param new_bmb_serial: The new BMB serial number
    :returns: The new module and spine serial numbers
    """
    # Get the rework asset information
    asset_id, line = __get_rework_asset_info()
    # Ensure we have the rework asset information
    if asset_id == -1 or line == -1:
        raise Exception('Cannot look up rework asset information')

    new_bmb = None
    # Check if the new BMB serial number is already consumed
    existing_bmb_id, existing_bmb_iv_id, consuming_spine_sn = __get_new_bmb_consumption(new_bmb_serial)
    if existing_bmb_id == -1:
        # Insert the new BMB
        new_bmb = serial_number.insert_serial_number(new_bmb_iv.ItemVersionId, new_bmb_serial, asset_id, operator)
    else:
        # Ensure the existing BMB is not already consumed into a spine
        if consuming_spine_sn:
            raise Exception('New BMB serial number {} is already consumed into spine {}'
                            .format(new_bmb_serial, consuming_spine_sn))
        # Ensure the planned BMB item version id matches the existing one
        if existing_bmb_iv_id != -1 and existing_bmb_iv_id != new_bmb_iv.ItemVersionId:
            raise Exception('New BMB exists with different item version id: {} != {}'
                            .format(existing_bmb_iv_id, new_bmb_iv.ItemVersionId))
        # Get the existing BMB serial number
        new_bmb = serial_number.get_serial_number_by_id(existing_bmb_id)
    # Ensure we obtained the new BMB
    if new_bmb.ItemSerialNoId == -1:
        raise Exception('Unable to insert or lookup new BMB serial number')

    # Generate the new module serial number
    new_module = serial_number.generate_serial_number(new_module_iv.ItemVersionId, asset_id, 'M', line, -1,
                                                      operator, True)
    # Ensure we generated the new module
    if new_module.ItemSerialNoId == -1:
        raise Exception('Cannot generate new module serial number')
    # Generate the new spine serial number
    new_spine = serial_number.generate_serial_number(new_spine_iv.ItemVersionId, asset_id, 'S', line, -1,
                                                     operator, True)
    # Ensure we generated the new spine
    if new_spine.ItemSerialNoId == -1:
        raise Exception('Cannot generate new spine serial number')
    # Insert and update additional records
    __update_records(old_module, old_spine, old_bmb,
                     new_module, new_spine, new_bmb,
                     new_module_iv, new_spine_iv, new_bmb_iv,
                     asset_id, operator)
    return new_module.SerialNoNumber, new_spine.SerialNoNumber


def __update_records(old_module, old_spine, old_bmb,
                     new_module, new_spine, new_bmb,
                     new_module_iv, new_spine_iv, new_bmb_iv,
                     asset_id, operator):
    """
    Update records pointing to old module, spine, and BMB

    :param old_module: The old module
    :param old_spine: The old spine
    :param old_bmb: The old BMB
    :param new_module: The new module
    :param new_spine: The new spine
    :param new_bmb: The new BMB
    :param new_module_iv: The new module item version information
    :param new_spine_iv: The new spine item version information
    :param new_bmb_iv: The new BMB item version information
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Begin the transaction
    tx = system.db.beginTransaction('MESDB')
    __update_spine_consumption(old_spine, new_spine, operator, tx)
    __update_bmb_consumption(old_bmb, new_bmb, operator, tx)
    __update_module_serial(old_module, new_module, operator, tx)
    __update_spine_serial(old_spine, new_spine, operator, tx)
    __update_module_genealogy(old_module, new_module, new_module_iv, tx)
    __update_spine_genealogy(old_spine, new_spine, new_spine_iv, tx)
    __update_bmb_genealogy(old_bmb, new_bmb, new_bmb_iv, tx)
    __update_module_tests(old_module, new_module, new_module_iv, tx)
    __update_spine_tests(old_spine, new_spine, tx)
    __inactivate_serial_numbers(old_module, old_spine, old_bmb, operator, tx)
    __insert_rework_events(old_module, old_spine, old_bmb,
                           new_module, new_spine, new_bmb,
                           asset_id, operator, tx)
    __insert_rework_log(old_module, old_spine, old_bmb,
                        new_module, new_spine, new_bmb,
                        operator, tx)
    # Commit and close the transaction
    system.db.commitTransaction(tx)
    system.db.closeTransaction(tx)


def __update_spine_consumption(old_spine, new_spine, operator, tx):
    """
    Update spine consumption records

    :param old_spine: The old spine
    :param new_spine: The new spine
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Define the query
    query = '''
        -- Update spine consumption to module
        UPDATE [MESDB].[mes].[Item_Consumption]
        SET ItemVersionId=?,
            FromSerialNoId=?,
            EditedBy=?,
            EditedAt=GETDATE()
        WHERE FromSerialNoId=?;
    '''
    # Define the arguments
    args = [new_spine.ItemVersionId, new_spine.ItemSerialNoId, operator, old_spine.ItemSerialNoId]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __get_new_bmb_consumption(new_bmb_serial):
    """
    Check if the new BMB serial number exists and is consumed

    :param new_bmb_serial: The new BMB serial number
    :type new_bmb_serial: str
    :returns: The ItemSerialNoId and ItemVersionId of the BMB, and SerialNoNumber of the spine if consumed
    :rtype: tuple
    """
    ItemSerialNoId = -1
    ItemVersionId = -1
    SerialNoNumber = ''
    try:
        # Define the query
        query = '''
            SELECT
                [isn1].[ItemSerialNoId],
                [isn1].[ItemVersionId],
                [isn2].[SerialNoNumber]
            FROM [mes].[Item_SerialNo] [isn1]
            LEFT JOIN [mes].[Item_Consumption] [ic]
            ON [isn1].[ItemSerialNoId]=[ic].[FromSerialNoId]
            LEFT JOIN [mes].[Item_SerialNo] [isn2]
            ON [ic].[ToSerialNoId]=[isn2].[ItemSerialNoId]
            WHERE [isn1].[SerialNoNumber]=?;
        '''
        # Define the arguments
        args = [new_bmb_serial]
        # Run the query
        results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
        # Get the results
        if results:
            ItemSerialNoId = int(results[0][0]) if results[0][0] else -1
            ItemVersionId = int(results[0][1]) if results[0][1] else -1
            SerialNoNumber = str(results[0][2]) if results[0][2] else None
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return ItemSerialNoId, ItemVersionId, SerialNoNumber


def __update_bmb_consumption(old_bmb, new_bmb, operator, tx):
    """
    Update BMB consumption records

    :param old_module: The old module
    :param old_spine: The old spine
    :param old_bmb: The old BMB
    :param new_module: The new module
    :param new_spine: The new spine
    :param new_bmb: The new BMB
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Define the query
    query = '''
        -- Update bmb consumption to spine
        UPDATE [MESDB].[mes].[Item_Consumption]
        SET ItemVersionId=?,
            FromSerialNoId=?,
            EditedBy=?,
            EditedAt=GETDATE()
        WHERE FromSerialNoId=?;
    '''
    # Define the arguments
    args = [new_bmb.ItemVersionId, new_bmb.ItemSerialNoId, operator, old_bmb.ItemSerialNoId]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_module_serial(old_module, new_module, operator, tx):
    """
    Update module serial consumption records

    :param old_module: The old module
    :param new_module: The new module
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Define the query
    query = '''
        -- Update module serial number id
        UPDATE [MESDB].[mes].[Item_Consumption]
        SET ToSerialNoId=?,
            EditedBy=?,
            EditedAt=GETDATE()
        WHERE ToSerialNoId=?;
    '''
    # Define the arguments
    args = [new_module.ItemSerialNoId, operator, old_module.ItemSerialNoId]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_spine_serial(old_spine, new_spine, operator, tx):
    """
    Update spine serial consumption records

    :param old_spine: The old spine
    :param new_spine: The new spine
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Define the query
    query = '''
        -- Update spine serial number id
        UPDATE [MESDB].[mes].[Item_Consumption]
        SET ToSerialNoId=?,
            EditedBy=?,
            EditedAt=GETDATE()
        WHERE ToSerialNoId=?;
    '''
    # Define the arguments
    args = [new_spine.ItemSerialNoId, operator, old_spine.ItemSerialNoId]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_module_genealogy(old_module, new_module, new_module_iv, tx):
    """
    Update module serial test result records

    :param old_module: The old module
    :param new_module: The new module
    :param new_module_iv: The new module item information
    :param tx: The transaction
    """
    for table in ('BMB', 'Cassette', 'CellLot', 'Spine', 'WeldedBlock'):
        # Define the query
        query = '''
            UPDATE [REPORTINGDB].[dbo].[BatteryModuleTo{}]
            SET ModuleSerialNumber=?,
                ModuleShortProduct=?,
                ModuleSerialNumberId=?
            WHERE ModuleSerialNumber=?;
        '''.format(table)
        # Define the arguments
        args = [new_module.SerialNoNumber,
                new_module_iv.ItemNo,
                new_module.ItemSerialNoId,
                old_module.SerialNoNumber]
        # Run the query
        system.db.runPrepUpdate(query, args, 'MESDB', tx)
    for table in ('Foil',):
        # Define the query
        query = '''
            UPDATE [REPORTINGDB].[dbo].[BatteryModuleTo{}]
            SET ModuleSerialNumber=?,
                ModuleSerialNumberId=?
            WHERE ModuleSerialNumber=?;
        '''.format(table)
        # Define the arguments
        args = [new_module.SerialNoNumber,
                new_module.ItemSerialNoId,
                old_module.SerialNoNumber]
        # Run the query
        system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_spine_genealogy(old_spine, new_spine, new_spine_iv, tx):
    """
    Update spine serial test result records

    :param old_spine: The old spine
    :param new_spine: The new spine
    :param new_spine_iv: The new spine item information
    :param tx: The transaction
    """
    # Define the query
    query = '''
        UPDATE [REPORTINGDB].[dbo].[BatteryModuleToSpine]
        SET SpineSerialNumber=?,
            SpinePartCode=?,
            SpinePartDesc=?,
            SpineSerialNumberId=?
        WHERE SpineSerialNumber=?;
    '''
    # Define the arguments
    args = [new_spine.SerialNoNumber,
            new_spine_iv.ItemNo,
            new_spine_iv.ItemDescription,
            new_spine.ItemSerialNoId,
            old_spine.SerialNoNumber]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_bmb_genealogy(old_bmb, new_bmb, new_bmb_iv, tx):
    """
    Update BMB serial test result records

    :param old_bmb: The old BMB
    :param new_bmb: The new BMB
    :param new_bmb_iv: The new BMB item information
    :param tx: The transaction
    """
    # Define the query
    query = '''
        UPDATE [REPORTINGDB].[dbo].[BatteryModuleToBMB]
        SET BMBSerialNumber=?,
            BMBPartCode=?,
            BMBPartDesc=?,
            BMBSerialNumberId=?
        WHERE BMBSerialNumber=?;
    '''
    # Define the arguments
    args = [new_bmb.SerialNoNumber,
            new_bmb_iv.ItemNo,
            new_bmb_iv.ItemDescription,
            new_bmb.ItemSerialNoId,
            old_bmb.SerialNoNumber]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_module_tests(old_module, new_module, new_module_iv, tx):
    """
    Update module serial test result records

    :param old_module: The old module
    :param new_module: The new module
    :param new_module_iv: The new module item information
    :param tx: The transaction
    """
    for test in ('BMBMEOL',
                 'FullMEOL',
                 'HiPotMEOL',
                 'ImpdncMEOL',
                 'IsoMEOL',
                 'MAD_CureReport',
                 'MAD_DispenseResult',
                 'Press_ModuleInspection'):
        # Define the query
        query = '''
            UPDATE [REPORTINGDB].[dbo].[TestResults_{}]
            SET SerialNumber=?,
                ItemNo=?,
                ProductionVersion=?,
                MES_ItemSerialNoId=?,
                MES_ItemVersionId=?
            WHERE MES_ItemSerialNoId=?;
        '''.format(test)
        # Define the arguments
        args = [new_module.SerialNoNumber,
                new_module_iv.ItemNo,
                new_module_iv.ProductionVersion,
                new_module.ItemSerialNoId,
                new_module.ItemVersionId,
                old_module.ItemSerialNoId]
        # Run the query
        system.db.runPrepUpdate(query, args, 'MESDB', tx)
    # Define the query
    query = '''
        -- Update module serial number id
        UPDATE [REPORTINGDB].[dbo].[TestResults_SpineIRHiPot]
        SET ModuleSerialNumber=?
        WHERE ModuleSerialNumber=?;
    '''
    # Define the arguments
    args = [new_module.SerialNoNumber, old_module.SerialNoNumber]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __update_spine_tests(old_spine, new_spine, tx):
    """
    Update spine serial test result records

    :param old_spine: The old spine
    :param new_spine: The new spine
    :param tx: The transaction
    """
    # Define the query
    query = '''
        -- Update spine serial number id
        UPDATE [REPORTINGDB].[dbo].[TestResults_SpineIRHiPot]
        SET SpineSerialNumber=?
        WHERE SpineSerialNumber=?;
    '''
    # Define the arguments
    args = [new_spine.SerialNoNumber, old_spine.SerialNoNumber]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __inactivate_serial_numbers(old_module, old_spine, old_bmb, operator, tx):
    """
    Inactivate old serial numbers

    :param old_module: The old module
    :param old_spine: The old spine
    :param old_bmb: The old BMB
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Get the serial number state id
    state_id = serial_number.get_serial_state_id('Inactive')
    # Set the module state
    serial_number.set_serial_state_id(old_module.ItemSerialNoId, state_id, operator, tx)
    # Set the spine state
    serial_number.set_serial_state_id(old_spine.ItemSerialNoId, state_id, operator, tx)
    # Set the BMB state
    serial_number.set_serial_state_id(old_bmb.ItemSerialNoId, state_id, operator, tx)


def __insert_rework_events(old_module, old_spine, old_bmb, new_module, new_spine, new_bmb, asset_id, operator, tx):
    """
    Insert events for a rework module

    :param old_module: The old module
    :param old_spine: The old spine
    :param old_bmb: The old BMB
    :param new_module: The new module
    :param new_spine: The new spine
    :param new_bmb: The new BMB
    :param asset_id: The asset id for the rework operation
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Define the query
    query = '''
        INSERT INTO [MESDB].[mes].[Item_Events] (
            [SerialNumberId],
            [AssetId],
            [CreatedBy],
            [CreatedAt],
            [ItemReasonId])
        VALUES (
            ?,
            ?,
            ?,
            GETDATE(),
            (SELECT TOP(1) ItemReasonId
            FROM [MESDB].[product].[Item_Reason]
            WHERE ItemReasonName LIKE '%BMB%Rework%'));
    '''
    for serial_id in (old_module.ItemSerialNoId, old_spine.ItemSerialNoId, old_bmb.ItemSerialNoId,
                      new_module.ItemSerialNoId, new_spine.ItemSerialNoId, new_bmb.ItemSerialNoId):
        # Define the arguments
        args = [serial_id, asset_id, operator]
        # Run the query
        system.db.runPrepUpdate(query, args, 'MESDB', tx)


def __insert_rework_log(old_module, old_spine, old_bmb, new_module, new_spine, new_bmb, operator, tx):
    """
    Insert log record for a rework module

    :param old_module: The old module
    :param old_spine: The old spine
    :param old_bmb: The old BMB
    :param new_module: The new module
    :param new_spine: The new spine
    :param new_bmb: The new BMB
    :param operator: The operator performing the rework operation
    :param tx: The transaction
    """
    # Define the query
    query = '''
        INSERT INTO [REVMES].[mes].[BMB_Rework_Log] (
            [Module_ItemSerialNoId],
            [Spine_ItemSerialNoId],
            [OLD_BMB_ItemSerialNoId],
            [NEW_Module_ItemSerialNoId],
            [NEW_Spine_ItemSerialNoId],
            [NEW_BMB_ItemSerialNoId],
            [OLD_Module_ItemVersionId],
            [OLD_Spine_ItemVersionId],
            [NEW_Module_ItemVersionId],
            [NEW_Spine_ItemVersionId],
            [ProcessedBy],
            [ProcessedAt])
        VALUES (
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            GETDATE());
    '''
    # Define the arguments
    args = [old_module.ItemSerialNoId, old_spine.ItemSerialNoId, old_bmb.ItemSerialNoId,
            new_module.ItemSerialNoId, new_spine.ItemSerialNoId, new_bmb.ItemSerialNoId,
            old_module.ItemVersionId, old_spine.ItemVersionId,
            new_module.ItemVersionId, new_spine.ItemVersionId,
            operator]
    # Run the query
    system.db.runPrepUpdate(query, args, 'MESDB', tx)
