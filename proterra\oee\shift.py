from java.lang import Exception as JavaException
import json
import traceback
import system.db
import system.util

_logger = system.util.getLogger('proterra.oee.shift')


def get_modal_states():
    """
    Get modal states

    :returns: All modal states
    """
    # Define the return data
    ret_vals = []
    # Define the query
    query = '''
        SELECT
            ModalStateId,
            ModalStateName,
            ModalStateDescription,
            ModalStateAvailable,
            Enabled
        FROM perf.ModalState
    '''
    # Define the arguments
    args = []
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'REPORTINGDB')
        # Get the results
        if results:
            for result in results:
                # Convert the return values
                ret_val = {
                    'ModalStateId': result['ModalStateId'],
                    'ModalStateName': result['ModalStateName'],
                    'ModalStateDescription': result['ModalStateDescription'],
                    'ModalStateAvailable': result['ModalStateAvailable'],
                    'Enabled': result['Enabled']
                }
                ret_vals.append(ret_val)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
        raise
    # Return the information
    return ret_vals


def get_shift_templates():
    """
    Get shift templates

    :returns: All shift templates
    """
    # Define the return data
    ret_vals = []
    # Define the query
    query = '''
        SELECT
            st.ShiftTemplateId,
            st.ShiftTemplateName,
            st.ShiftTemplateDescription,
            st.Enabled,
            st.EditedAt,
            (SELECT DISTINCT
                sta.AssetId
                FROM perf.ShiftTemplateAssets sta
                WHERE st.ShiftTemplateId=sta.ShiftTemplateId
                FOR JSON PATH) AS Assets,
            (SELECT DISTINCT
                std.DayId,
                std.StartOffsetMinsFromMidnight,
                std.EndOffsetMinsFromMidnight,
                std.Enabled,
                (SELECT DISTINCT
                    stdms.ShiftTemplateDayModalStateId,
                    stdms.ModalStateId,
                    stdms.StartOffsetMinsFromMidnight,
                    stdms.EndOffsetMinsFromMidnight,
                    (SELECT DISTINCT
                        stdmsa.AssetId
                        FROM perf.ShiftTemplateDayModalStateAssets stdmsa
                        WHERE stdms.ShiftTemplateDayModalStateId=stdmsa.ShiftTemplateDayModalStateId
                        FOR JSON PATH) AS Assets
                    FROM perf.ShiftTemplateDayModalState stdms
                    WHERE std.ShiftTemplateId=stdms.ShiftTemplateId AND std.DayId=stdms.DayId
                    FOR JSON PATH) AS ModalStates
                FROM perf.ShiftTemplateDay std
                WHERE st.ShiftTemplateId=std.ShiftTemplateId
                FOR JSON PATH) AS Days,
            (SELECT DISTINCT
                stt.ShiftTemplateTargetId,
                stt.StandardRate,
                stt.ScheduleRate,
                stt.Period,
                (SELECT DISTINCT
                    stta.AssetId
                    FROM perf.ShiftTemplateTargetAssets stta
                    WHERE stt.ShiftTemplateTargetId=stta.ShiftTemplateTargetId
                    FOR JSON PATH) AS Assets
                FROM perf.ShiftTemplateTarget stt
                WHERE st.ShiftTemplateId=stt.ShiftTemplateId
                FOR JSON PATH) AS Targets
        FROM perf.ShiftTemplate st
    '''
    # Define the arguments
    args = []
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'REPORTINGDB')
        # Get the results
        if results:
            for result in results:
                # Convert the return values
                ret_val = {
                    'ShiftTemplateId': result['ShiftTemplateId'],
                    'ShiftTemplateName': result['ShiftTemplateName'],
                    'ShiftTemplateDescription': result['ShiftTemplateDescription'],
                    'Enabled': result['Enabled'],
                    'EditedAt': result['EditedAt'],
                    'Assets': json.loads(result['Assets']) if result['Assets'] else [],
                    'Days': json.loads(result['Days']) if result['Days'] else [],
                    'Targets': json.loads(result['Targets']) if result['Targets'] else []
                }
                ret_vals.append(ret_val)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
        raise
    # Return the information
    return ret_vals


def get_equipment_shifts(start_date, end_date):
    """
    Get equipment shifts

    :param start_date: The minimum date to search
    :param end_date: The maximum date to search
    :returns: All equipment shifts
    """
    # Define the return data
    ret_vals = []
    # Define the query
    query = '''
        SELECT
            es.EquipmentShiftId,
            es.Name,
            es.Description,
            es.StartTime,
            es.EndTime,
            es.Canceled,
            (SELECT DISTINCT
                esa.AssetId
                FROM perf.EquipmentShiftAssets esa
                WHERE es.EquipmentShiftId=esa.EquipmentShiftId
                FOR JSON PATH) AS Assets,
            (SELECT DISTINCT
                esms.EquipmentShiftModalStateId,
                esms.ModalStateName,
                esms.ModalStateDescription,
                esms.ModalStateAvailable,
                esms.StartTime,
                esms.EndTime,
                esms.Canceled,
                (SELECT DISTINCT
                    esmsa.AssetId
                    FROM perf.EquipmentShiftModalStateAssets esmsa
                    WHERE esms.EquipmentShiftModalStateId=esmsa.EquipmentShiftModalStateId
                    FOR JSON PATH) AS Assets
                FROM perf.EquipmentShiftModalState esms
                WHERE es.EquipmentShiftId=esms.EquipmentShiftId
                FOR JSON PATH) AS ModalStates,
            (SELECT DISTINCT
                est.EquipmentShiftTargetId,
                est.StandardRate,
                est.ScheduleRate,
                est.Period,
                est.Canceled,
                (SELECT DISTINCT
                    esta.AssetId
                    FROM perf.EquipmentShiftTargetAssets esta
                    WHERE est.EquipmentShiftTargetId=esta.EquipmentShiftTargetId
                    FOR JSON PATH) AS Assets
                FROM perf.EquipmentShiftTarget est
                WHERE es.EquipmentShiftId=est.EquipmentShiftId
                FOR JSON PATH) AS Targets
        FROM perf.EquipmentShift es
        WHERE es.StartTime >= ?
        AND es.EndTime <= ?
        ORDER BY StartTime
    '''
    # Define the arguments
    args = [start_date, end_date]
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'REPORTINGDB')
        # Get the results
        if results:
            for result in results:
                # Convert the return values
                ret_val = {
                    'EquipmentShiftId': result['EquipmentShiftId'],
                    'Name': result['Name'],
                    'Description': result['Description'],
                    'StartTime': result['StartTime'],
                    'EndTime': result['EndTime'],
                    'Canceled': result['Canceled'],
                    'Assets': json.loads(result['Assets']) if result['Assets'] else [],
                    'ModalStates': json.loads(result['ModalStates']) if result['ModalStates'] else [],
                    'Targets': json.loads(result['Targets']) if result['Targets'] else []
                }
                ret_vals.append(ret_val)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
        raise
    # Return the information
    return ret_vals


def insert_equipment_shifts(equipment_shifts):
    """
    Insert equipment shifts

    :param shifts: An array of shifts
    """
    # Define the db
    db = 'REPORTINGDB'
    # Define the query
    query1 = '''
        INSERT INTO perf.EquipmentShift(
            Name,
            Description,
            StartTime,
            EndTime
        )
        VALUES(
            ?,
            ?,
            ?,
            ?
        )
    '''
    query2 = '''
        INSERT INTO perf.EquipmentShiftAssets(
            EquipmentShiftId,
            AssetId
        )
        VALUES(
            ?,
            ?
        )
    '''
    query3 = '''
        INSERT INTO perf.EquipmentShiftModalState(
            EquipmentShiftId,
            ModalStateName,
            ModalStateDescription,
            ModalStateAvailable,
            StartTime,
            EndTime
        )
        VALUES(
            ?,
            ?,
            ?,
            ?,
            ?,
            ?
        )
    '''
    query4 = '''
        INSERT INTO perf.EquipmentShiftModalStateAssets(
            EquipmentShiftModalStateId,
            AssetId
        )
        VALUES(
            ?,
            ?
        )
    '''
    query5 = '''
        INSERT INTO perf.EquipmentShiftTarget(
            EquipmentShiftId,
            StandardRate,
            ScheduleRate,
            Period
        )
        VALUES(
            ?,
            ?,
            ?,
            ?
        )
    '''
    query6 = '''
        INSERT INTO perf.EquipmentShiftTargetAssets(
            EquipmentShiftTargetId,
            AssetId
        )
        VALUES(
            ?,
            ?
        )
    '''
    try:
        for equipment_shift in equipment_shifts:
            # Define the arguments
            args1 = [
                equipment_shift['Name'],
                equipment_shift['Description'],
                equipment_shift['StartTime'],
                equipment_shift['EndTime']
            ]
            # Run the query
            equipment_shift_id = system.db.runPrepUpdate(query1, args1, db, getKey=True)
            for equipment_shift_asset in equipment_shift['Assets']:
                # Define the arguments
                args2 = [
                    equipment_shift_id,
                    int(equipment_shift_asset['AssetId'])
                ]
                # Run the query
                system.db.runPrepUpdate(query2, args2, db)
            for equipment_shifts_modal_state in equipment_shift['ModalStates']:
                # Define the arguments
                args3 = [
                    equipment_shift_id,
                    equipment_shifts_modal_state['ModalStateName'],
                    equipment_shifts_modal_state['ModalStateDescription'],
                    equipment_shifts_modal_state['ModalStateAvailable'],
                    equipment_shifts_modal_state['StartTime'],
                    equipment_shifts_modal_state['EndTime']
                ]
                # Run the query
                equipment_shift_modal_state_id = system.db.runPrepUpdate(query3, args3, db, getKey=True)
                for equipment_shift_modal_state_asset in equipment_shifts_modal_state['Assets']:
                    # Define the arguments
                    args4 = [
                        equipment_shift_modal_state_id,
                        int(equipment_shift_modal_state_asset['AssetId'])
                    ]
                    # Run the query
                    system.db.runPrepUpdate(query4, args4, db)
            for equipment_shifts_target in equipment_shift['Targets']:
                # Define the arguments
                args5 = [
                    equipment_shift_id,
                    equipment_shifts_target['StandardRate'],
                    equipment_shifts_target['ScheduleRate'],
                    equipment_shifts_target['Period']
                ]
                # Run the query
                equipment_shift_target_id = system.db.runPrepUpdate(query5, args5, db, getKey=True)
                for equipment_shift_target_asset in equipment_shifts_target['Assets']:
                    # Define the arguments
                    args6 = [
                        equipment_shift_target_id,
                        int(equipment_shift_target_asset['AssetId'])
                    ]
                    # Run the query
                    system.db.runPrepUpdate(query6, args6, db)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
        raise
