@font-face {
    font-family: "Noto Sans";
    font-style: normal;
    font-weight: 400;
    src: local('Noto Sans'), local('NotoSans'),
         url('/data/perspective/fonts/noto-sans-v8-latin-regular.ttf') format('truetype');
}

@font-face {
    font-family: "Noto Sans";
    font-style: normal;
    font-weight: 500;
    src: local('Noto Sans Medium'), local('NotoSans-Medium'),
         url('/data/perspective/fonts/noto-sans-v8-latin-medium.ttf') format('truetype');
}

@font-face {
    font-family: "Noto Sans";
    font-style: normal;
    font-weight: 700;
    src: local('Noto Sans Bold'), local('NotoSans-Bold'),
         url('/data/perspective/fonts/noto-sans-v8-latin-700.ttf') format('truetype');
}

@font-face {
    font-family: "Roboto";
    src: url("/data/perspective/fonts/Roboto-Regular.woff");
}

@font-face {
    font-family: "Roboto";
    font-weight: 500;
    src: url("/data/perspective/fonts/Roboto-Medium.woff");
}

@font-face {
    font-family: "Roboto";
    font-weight: 300;
    src: url("/data/perspective/fonts/Roboto-Light.woff");
}

@font-face {
    font-family: "Roboto";
    font-weight: 700;
    src: url("/data/perspective/fonts/Roboto-Bold.woff");
}

@font-face {
    font-family: "Roboto-italic";
    font-style: italic;
    src: url("/data/perspective/fonts/Roboto-Italic.woff");
}

@font-face {
    font-family: "Roboto-mono";
    src: url("/data/perspective/fonts/RobotoMono-Regular.woff");
}

@font-face {
    font-family: "Roboto-condensed";
    src: url("/data/perspective/fonts/RobotoCondensed-Regular.woff");
}
