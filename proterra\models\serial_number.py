from java.lang import Exception as JavaException
from datetime import datetime
from system.db import BIT, INTEGER, NVARCHAR
import traceback
import system.dataset
import system.db
import system.util

_logger = system.util.getLogger('proterra.models.serial_number')


class SerialNumberException(Exception):
    """
    Exception that can be raised and caught for serial number errors
    """
    pass


class Item_SerialNo:
    """
    ORM representation of the [MESDB].[mes].[Item_SerialNo] table
    """
    ItemSerialNoId = -1
    ItemVersionId = -1
    SerialNoNumber = ''
    ItemSerialNoStateId = -1
    AssetId = -1
    CreatedBy = ''
    CreatedAt = datetime(1970, 1, 1)
    EditedBy = ''
    EditedAt = datetime(1970, 1, 1)


def get_serial_number_by_id(serial_id, use_replica=True):
    """
    Get a serial number model from the supplied serial number id

    :param serial_id: The serial number id
    :param use_replica: True if the replica database should be used
    :returns: The serial number model
    """
    # Define the query
    query = '''
        SELECT TOP(1)
            [ItemSerialNoId],
            [ItemVersionId],
            [SerialNoNumber],
            [ItemSerialNoStateId],
            [AssetId],
            [CreatedBy],
            [CreatedAt],
            [EditedBy],
            [EditedAt]
        FROM [MESDB].[mes].[Item_SerialNo]
        WHERE [ItemSerialNoId]=?;
    '''
    # Define the arguments
    args = [serial_id]
    # Return the serial number model
    return __get_serial_number(query, args, use_replica)


def get_serial_number_by_number(serial, use_replica=True):
    """
    Get a serial number model from the supplied serial number

    :param serial: The serial number
    :param use_replica: True if the replica database should be used
    :returns: The serial number model
    """
    # Define the query
    query = '''
        SELECT TOP(1)
            [ItemSerialNoId],
            [ItemVersionId],
            [SerialNoNumber],
            [ItemSerialNoStateId],
            [AssetId],
            [CreatedBy],
            [CreatedAt],
            [EditedBy],
            [EditedAt]
        FROM [MESDB].[mes].[Item_SerialNo]
        WHERE [SerialNoNumber]=?;
    '''
    # Define the arguments
    args = [serial]
    # Return the serial number model
    return __get_serial_number(query, args, use_replica)


def get_serial_number_by_barcode(barcode, use_replica=True):
    """
    Get a serial number model from the supplied barcode

    :param barcode: The barcode
    :param use_replica: True if the replica database should be used
    :returns: The serial number model
    """
    # Replace | with %
    like = barcode.replace('|', '%')
    # Replace _ with %
    like = like.replace('_', '%')
    # Replace / with %
    like = like.replace('/', '%')
    # Remove any whitespace in the string
    like = ''.join(like.split())
    # Surround with %
    like = '%{}%'.format(like)
    # Define the query
    query = '''
        SELECT TOP(1)
            [ItemSerialNoId],
            [ItemVersionId],
            [SerialNoNumber],
            [ItemSerialNoStateId],
            [AssetId],
            [CreatedBy],
            [CreatedAt],
            [EditedBy],
            [EditedAt]
        FROM [MESDB].[mes].[Item_SerialNo]
        WHERE [SerialNoNumber]=?
        OR [SerialNoNumber] LIKE ?;
    '''
    # Define the arguments
    args = [barcode, like]
    # Return the serial number model
    return __get_serial_number(query, args, use_replica)


def __get_serial_number(query, args, use_replica):
    # Define the return model
    ret_model = Item_SerialNo()
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'MESDB_Replica' if use_replica else 'MESDB')
        # Get the results
        if results:
            ItemSerialNoId = results[0]['ItemSerialNoId']
            ItemVersionId = results[0]['ItemVersionId']
            SerialNoNumber = results[0]['SerialNoNumber']
            ItemSerialNoStateId = results[0]['ItemSerialNoStateId']
            AssetId = results[0]['AssetId']
            CreatedBy = results[0]['CreatedBy']
            CreatedAt = results[0]['CreatedAt']
            EditedBy = results[0]['EditedBy']
            EditedAt = results[0]['EditedAt']
            # Convert the return values
            ret_model.ItemSerialNoId = int(ItemSerialNoId) if ItemSerialNoId else -1
            ret_model.ItemVersionId = int(ItemVersionId) if ItemVersionId else -1
            ret_model.SerialNoNumber = str(SerialNoNumber) if SerialNoNumber else ''
            ret_model.ItemSerialNoStateId = int(ItemSerialNoStateId) if ItemSerialNoStateId else -1
            ret_model.AssetId = int(AssetId) if AssetId else -1
            ret_model.CreatedBy = str(CreatedBy) if CreatedBy else ''
            ret_model.CreatedAt = datetime.fromtimestamp(CreatedAt.getTime() / 1000.0) \
                if CreatedAt else datetime(1970, 1, 1)
            ret_model.EditedBy = str(EditedBy) if EditedBy else ''
            ret_model.EditedAt = datetime.fromtimestamp(EditedAt.getTime() / 1000.0) \
                if EditedAt else datetime(1970, 1, 1)
    except (Exception, JavaException):
        msg = traceback.format_exc()
        _logger.error(msg)
        raise SerialNumberException(msg)
    # Return the serial number model
    return ret_model


def insert_serial_number(item_version_id, serial, asset_id, operator):
    """
    Insert a serial number into the MES database

    :param item_version_id: The item version id
    :param serial: The serial number
    :param asset_id: The asset where the operation is happening
    :param operator: The operator performing the operation
    :returns: The inserted serial number model
    """
    # Define the return model
    ret_model = Item_SerialNo()
    try:
        # Create the sproc call
        sproc = system.db.createSProcCall('[MESDB].[mes].[sp_INSERT_ItemSerialNo]', 'MESDB')
        # Define the sproc parameters
        sproc.registerInParam('ItemVersionId', INTEGER, item_version_id)
        sproc.registerInParam('SerialNoNumber', NVARCHAR, serial)
        sproc.registerInParam('ItemSerialNoStateId', INTEGER, 1)
        sproc.registerInParam('AssetId', INTEGER, asset_id)
        sproc.registerInParam('CreatedBy', NVARCHAR, operator)
        # Execute the sproc
        system.db.execSProcCall(sproc)
        # Get the result datset
        results = system.dataset.toPyDataSet(sproc.getResultSet())
        # Check the sproc result
        __check_sproc_result(results)
        # Get the return model
        # Don't use the replica database since the serial number hasn't synced yet
        ret_model = get_serial_number_by_number(serial, False)
    except (Exception, JavaException):
        msg = traceback.format_exc()
        _logger.error(msg)
        raise SerialNumberException(msg)
    # Return the serial number model
    return ret_model


def generate_serial_number(item_version_id, asset_id, prefix, line, work_order, operator, bypass_work_order=False):
    """
    Generate and insert a serial number into the MES database

    :param item_version_id: The item version id
    :param asset_id: The asset where the operation is happening
    :param prefix: The prefix for the generated serial number
    :param line: The line where the operation is happening
    :param work_order: The work order producing the serial number
    :param operator: The operator performing the operation
    :param bypass_work_order: True if the serial number should be generated without a work order
    :returns: The inserted serial number model
    """
    # Define the return model
    ret_model = Item_SerialNo()
    try:
        # Create the sproc call
        sproc = system.db.createSProcCall('[MESDB].[mes].[sp_GENERATE_ItemSerialNo]', 'MESDB')
        # Define the sproc parameters
        sproc.registerInParam('ItemVersionId', INTEGER, item_version_id)
        sproc.registerInParam('AssetId', INTEGER, asset_id)
        sproc.registerInParam('PartTypeLetter', NVARCHAR, prefix)
        sproc.registerInParam('LineNumber', INTEGER, line)
        sproc.registerInParam('WorkOrderId', INTEGER, work_order)
        sproc.registerInParam('Username', NVARCHAR, operator)
        sproc.registerInParam('is_debug', INTEGER, 0)
        sproc.registerInParam('debug_waittimesec', INTEGER, 0)
        sproc.registerInParam('bypassWorkOrder', BIT, 1 if bypass_work_order else 0)
        sproc.registerOutParam('SerialNumberGenerated', NVARCHAR)
        # Execute the sproc
        system.db.execSProcCall(sproc)
        # Get the result datset
        results = system.dataset.toPyDataSet(sproc.getResultSet())
        # Check the sproc result
        __check_sproc_result(results)
        # Get the return model
        # Don't use the replica database since the serial number hasn't synced yet
        ret_model = get_serial_number_by_number(sproc.getOutParamValue('SerialNumberGenerated'), False)
    except (Exception, JavaException):
        msg = traceback.format_exc()
        _logger.error(msg)
        raise SerialNumberException(msg)
    # Return the serial number model
    return ret_model


def __check_sproc_result(results):
    """
    Check that the sproc result was successful
    """
    # Ensure we got results
    if not results:
        raise Exception('Stored procedure returned no results')
    # Get the results
    ret_result = results[0]['Result']
    ret_result_message = results[0]['ResultMessage']
    # Convert the return values
    result = str(ret_result) if ret_result else ''
    result_message = str(ret_result_message) if ret_result_message else ''
    # Check the result
    if result != 'Success':
        raise Exception(result_message)


def get_serial_state_id(state_name):
    """
    Get the serial state id based on a state name

    :param state_name: The serial state name
    :param tx: An optional transaction
    :returns: The serial state id
    """
    state_id = -1
    try:
        # Define the query
        query = '''
            SELECT TOP(1) ItemSerialNoStateId
            FROM [MESDB].[mes].[Item_SerialNo_State]
            WHERE ItemSerialNoStateName LIKE ?;
        '''
        # Define the arguments
        args = ['%{}%'.format(state_name)]
        # Run the query
        result = system.db.runScalarPrepQuery(query, args, 'MESDB_Replica')
        # Get the result
        state_id = int(result) if result else -1
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return state_id


def get_serial_state_name(state_id):
    """
    Get the serial state id based on a state name

    :param state_id: The serial state id
    :param tx: An optional transaction
    :returns: The serial state name
    """
    state_name = ''
    try:
        # Define the query
        query = '''
            SELECT TOP(1) ItemSerialNoStateName
            FROM [MESDB].[mes].[Item_SerialNo_State]
            WHERE ItemSerialNoStateId=?;
        '''
        # Define the arguments
        args = [state_id]
        # Run the query
        result = system.db.runScalarPrepQuery(query, args, 'MESDB_Replica')
        # Get the result
        state_name = str(result) if result else ''
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return state_name


def set_serial_state_id(serial_id, state_id, operator, tx=None):
    """
    Set the serial state for a serial number

    :param serial_id: The serial id
    :param state_id: The serial state id
    :param operator: The operator performing the action
    :param tx: An optional transaction
    """
    try:
        # Define the query
        query = '''
            UPDATE [MESDB].[mes].[Item_SerialNo]
            SET [ItemSerialNoStateId]=?,
                [EditedBy]=?,
                [EditedAt]=GETDATE()
            WHERE [ItemSerialNoId]=?
        '''
        # Define the arguments
        args = [state_id, operator, serial_id]
        # Run the query
        system.db.runPrepUpdate(query, args, '' if tx else 'MESDB', tx)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())


def set_serial_state_name(serial_id, state_name, operator, tx=None):
    """
    Set the serial state for a serial number

    :param serial_id: The serial id
    :param state_name: The serial state name
    :param operator: The operator performing the action
    :param tx: An optional transaction
    """
    # Get the state id
    state_id = get_serial_state_id(state_name)
    # Set the serial state id
    set_serial_state_id(serial_id, state_id, operator, tx)
