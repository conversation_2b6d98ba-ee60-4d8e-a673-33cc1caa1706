{"name": "Python 2.7 Ignition Environment", "image": "docker.io/python:2.7.18", "postStartCommand": "/usr/local/bin/python -m pip install --upgrade pip setuptools wheel && /usr/local/bin/python -m pip install --upgrade -r requirements.txt", "customizations": {"vscode": {"extensions": ["ms-python.python"]}, "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.analysis.typeCheckingMode": "off", "python.analysis.typeEvaluation.analyzeUnannotatedFunctions": false, "python.analysis.diagnosticSeverityOverrides": {"reportMissingParameterType": "none", "reportUnknownParameterType": "none"}}}}