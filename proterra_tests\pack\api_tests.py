import platform
from proterra.pack import api as pack_api
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner
from mock import patch, MagicMock


@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class PackInfoTests(TestCase):
    def test1(self):
        json_string = [[{
            'jsonResult': '[{\"Part\":{\"PartCode\":\"066300\",\"PartDesc\":\"ASSEMBLY, S1-15-6S BATTERY, INVERTED, DAIMLER\",\"PartRevision\":\"A01\",\"PartID\":579138},\"workorder\":{\"MfgWorksOrderId\":109672,\"WorksOrderNumber\":\"GVB-1399538_PL2\",\"OrderQty\":1.500000000000000e+001,\"QtyMade\":1.500000000000000e+001,\"Status\":40,\"StatusName\":\"Completed\",\"Facility\":\"GVB\",\"EditUser\":\"JSBryant\",\"EditDate\":\"2024-12-03T17:48:20.007\",\"RouteVersionId\":20719,\"RouteID\":2951,\"SegmentID\":192,\"SegmentName\":\"GVB-BATTERYPACKLINE\"},\"serialnumber\":{\"MfgWorksOrderId\":109672,\"MfgLotID\":9737213,\"MfgLotSequence\":13,\"MfgSerialNumberId\":8962726,\"SerialNumber\":\"2000026250\"}}]'
        }]]
        pack_info = pack_api._parse_pack_info(json_string)
        self.assertEqual('GVB-BATTERYPACKLINE', pack_info['displayName'])
        self.assertEqual('ASSEMBLY, S1-15-6S BATTERY, INVERTED, DAIMLER', pack_info['itemDesc'])
        self.assertEqual('2000026250', pack_info['serialNumber'])
        self.assertEqual('A01', pack_info['rev'])
        self.assertEqual('Completed', pack_info['itemSerialNoStateName'])
        self.assertEqual('066300', pack_info['itemNo'])
        self.assertEqual(8962726, pack_info['serialNumberId'])


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            PackInfoTests,
            PackInfoTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
