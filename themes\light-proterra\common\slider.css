
.ia_slider {
    border-radius: 6px;
}

.ia_slider__rail {
    background-color: var(--neutral-40);
}

.ia_slider__track {
    border-radius: var(--borderRadius);
    background-color: var(--callToAction);
}

.ia_slider__track--disabled {
    background-color: var(--border--disabled);
}

.ia_slider__step__dot {
    border: 2px solid var(--border);
    background-color: var(--container);
    border-radius: 50%;
}

.ia_slider__step__dot--active {
    border-color: var(--callToAction);
}

.ia_slider__handle {
    border-radius: 50%;
    border: solid 1px var(--border);
    background-color: var(--container);
}

.ia_slider__handle:hover {
    background-color: var(--containerRoot);
    border-color: var(--callToAction);
}

.ia_slider__handle:active {
    border-color: var(--callToAction);
    background-color: var(--callToActionHighlight);
    box-shadow: 0 0 5px rgba(78, 188, 252, 0.2);
}

.ia_slider__handle:focus {
    border-color: var(--callToAction--active);
    box-shadow: 0 0 0 5px rgba(78, 188, 252, 0.5);
}

.ia_slider__handle--disabled {
    border-color: var(--border--disabled);
    background-color: var(--border--disabled);
}

.ia_slider__mark__text {
    color: var(--neutral-60);
}

.ia_slider__mark__text--active {
    color: var(--neutral-90);
}

.ia_slider__mark__text--disabled {
    color: var(--label--disabled);
}
