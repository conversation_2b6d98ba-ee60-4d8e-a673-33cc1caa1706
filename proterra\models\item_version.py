from java.lang import Exception as JavaException
from datetime import datetime
import traceback
import system.dataset
import system.db
import system.util

_logger = system.util.getLogger('proterra.models.item_version')


class ItemVersionException(Exception):
    """
    Exception that can be raised and caught for item version errors
    """
    pass


class Item_Version:
    """
    ORM representation of the [MESDB].[product].[Item_Version] table
    """
    ItemVersionId = -1
    ItemId = -1
    ItemNo = ''
    ItemDescription = ''
    ProductionVersion = ''
    CreatedBy = ''
    CreatedAt = datetime(1970, 1, 1)
    EditedBy = ''
    EditedAt = datetime(1970, 1, 1)


def get_item_version_by_id(item_id):
    """
    Get the item version information

    :param item_id: The item id
    :returns: The item version model
    """
    # Define the query
    query = '''
        SELECT TOP(1)
        ItemVersionId,
        ItemId,
        ItemNo,
        ItemDescription,
        ProductionVersion,
        CreatedBy,
        CreatedAt,
        EditedBy,
        EditedAt
        FROM [MESDB].[product].[Item_Version]
        WHERE ItemVersionId=?;
    '''
    # Define the arguments
    args = [item_id]
    # Return the model
    return __get_item_version(query, args)


def get_item_version_by_no(item_no, rev=''):
    """
    Get the item version information

    :param item_no: The item number
    :param rev: The item rev, or empty string for the latest rev
    :returns: The item version model
    """
    # Define the query
    query = ''
    args = []
    if rev:
        query = '''
            SELECT TOP(1)
            ItemVersionId,
            ItemId,
            ItemNo,
            ItemDescription,
            ProductionVersion,
            CreatedBy,
            CreatedAt,
            EditedBy,
            EditedAt
            FROM [MESDB].[product].[Item_Version]
            WHERE ItemNo=?
            AND ProductionVersion=?;
        '''
        # Define the arguments
        args = [item_no, rev]
    else:
        query = '''
            SELECT TOP(1)
            ItemVersionId,
            ItemId,
            ItemNo,
            ItemDescription,
            ProductionVersion,
            CreatedBy,
            CreatedAt,
            EditedBy,
            EditedAt
            FROM [MESDB].[product].[Item_Version]
            WHERE ItemNo=?
            ORDER BY ItemVersionId DESC;
        '''
        # Define the arguments
        args = [item_no]
    # Return the model
    return __get_item_version(query, args)


def __get_item_version(query, args):
    # Define the return model
    ret_model = Item_Version()
    try:
        # Run the query
        results = system.db.runPrepQuery(query, args, 'MESDB_Replica')
        # Get the results
        if results:
            ItemVersionId = results[0]['ItemVersionId']
            ItemId = results[0]['ItemId']
            ItemNo = results[0]['ItemNo']
            ItemDescription = results[0]['ItemDescription']
            ProductionVersion = results[0]['ProductionVersion']
            CreatedBy = results[0]['CreatedBy']
            CreatedAt = results[0]['CreatedAt']
            EditedBy = results[0]['EditedBy']
            EditedAt = results[0]['EditedAt']
            # Convert the return values
            ret_model.ItemVersionId = int(ItemVersionId) if ItemVersionId else -1
            ret_model.ItemId = int(ItemId) if ItemId else -1
            ret_model.ItemNo = str(ItemNo) if ItemNo else ''
            ret_model.ItemDescription = str(ItemDescription) if ItemDescription else ''
            ret_model.ProductionVersion = str(ProductionVersion) if ProductionVersion else ''
            ret_model.CreatedBy = str(CreatedBy) if CreatedBy else ''
            ret_model.CreatedAt = datetime.fromtimestamp(CreatedAt.getTime() / 1000.0) \
                if CreatedAt else datetime(1970, 1, 1)
            ret_model.EditedBy = str(EditedBy) if EditedBy else ''
            ret_model.EditedAt = datetime.fromtimestamp(EditedAt.getTime() / 1000.0) \
                if EditedAt else datetime(1970, 1, 1)
    except (Exception, JavaException):
        msg = traceback.format_exc()
        _logger.error(msg)
        raise ItemVersionException(msg)
    # Return the model
    return ret_model
