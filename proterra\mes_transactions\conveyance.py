from proterra.mes_transactions import txn, util
import system.date
import system.util


def palletMovement(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData', '/Parameters.PalletSerialNumberTagPath', '/Parameters.TriggerSerialNumberTagPath'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "PalletID" in txnData["PLCData"].keys():
                palletSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["PalletID"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/PalletSerialNumber"], [palletSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_Conveyance_PalletMovement]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Inserted PerformTXN body
        sprocResults = dict(txn.SPROC_RESULTS)
        #######################
        # SPROC
        if not readError:
            sprocResults = dict(txn.runSproc(call))
        else:
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = "Values missing in PLC Data or Null Inputs"

        # If a Pallet SN Tag Path is defined or if TriggerSN Tag Path is defined
        if sprocResults['Success']:
            if len(palletSerialNumber) >= 4 and len(txnData['Parameters.PalletSerialNumberTagPath']) > 10:
                system.tag.writeBlocking(txnData['Parameters.PalletSerialNumberTagPath'], palletSerialNumber)
            if len(txnData['Parameters.TriggerSerialNumberTagPath']) > 10:
                system.tag.writeBlocking(txnData['Parameters.TriggerSerialNumberTagPath'], 1)
        #######################
        # RESPONSE
        txn.writeResponse(txnData['PathRESP'], sprocResults)
        #######################
        # RSD
        txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                         sprocResults['ResultCode'], txnData['PathRSD'], baseTagPath)
        #######################
        # TXN END
        exclude_keys = {'PathRSD', 'PathRESP', 'Parameters.PalletSerialNumberTagPath',
                        'Parameters.TriggerSerialNumberTagPath'}
        dataPayload = {k: txnData[k] for k in set(list(txnData.keys())) - exclude_keys}
        txn.txnLogEntry(txnData['ProcessStep'], baseTagPath, sprocResults, dataPayload, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def palletMovement_OperatorStation(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData', '/Parameters.PalletSerialNumberTagPath', '/Parameters.TriggerSerialNumberTagPath'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "PalletID" in txnData["PLCData"].keys():
                palletSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["PalletID"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/PalletSerialNumber"], [palletSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_Conveyance_PalletMovement]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Inserted PerformTXN body
        sprocResults = dict(txn.SPROC_RESULTS)
        #######################
        # SPROC
        if not readError:
            sprocResults = dict(txn.runSproc(call))
        else:
            sprocResults['ResultCode'] = 1015
            sprocResults['ResultMessage'] = "Values missing in PLC Data or Null Inputs"

        # If a Pallet SN Tag Path is defined or if TriggerSN Tag Path is defined
        if sprocResults['Success']:
            if len(palletSerialNumber) >= 4 and len(txnData['Parameters.PalletSerialNumberTagPath']) > 10:
                system.tag.writeBlocking(txnData['Parameters.PalletSerialNumberTagPath'], palletSerialNumber)
            if len(txnData['Parameters.TriggerSerialNumberTagPath']) > 10:
                system.tag.writeBlocking(txnData['Parameters.TriggerSerialNumberTagPath'], 1)
        #######################
        # RESPONSE
        txn.writeResponse(txnData['PathRESP'], sprocResults)
        #######################
        # RSD
        txn.determineRSD(sprocResults['Success'], sprocResults['ResultMessage'],
                         sprocResults['ResultCode'], txnData['PathRSD'], baseTagPath)
        #######################
        # TXN END
        exclude_keys = {'PathRSD', 'PathRESP', 'Parameters.PalletSerialNumberTagPath',
                        'Parameters.TriggerSerialNumberTagPath'}
        dataPayload = {k: txnData[k] for k in set(list(txnData.keys())) - exclude_keys}
        txn.txnLogEntry(txnData['ProcessStep'], baseTagPath, sprocResults, dataPayload, txn_start)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def palletMovement_AB(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId_A', '/Inputs/WorkOrderId_A', '/Inputs/ItemVersionId_B', '/Inputs/WorkOrderId_B', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/PathRESP', '/Inputs/PathRSD', '/Inputs/ProcessStep', '/PLCData'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            if "PalletID" in txnData["PLCData"].keys():
                palletSerialNumber = util.parseBarcode_Last(txnData["PLCData"]["PalletID"])
                # Write values to UDT tags
                system.tag.writeBlocking([baseTagPath+"/PalletSerialNumber"], [palletSerialNumber])
            else:
                raise Exception("Values missing in PLCData")
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_Conveyance_PalletMovement_AB]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId_A", system.db.INTEGER, txnData['ItemVersionId_A'])
            call.registerInParam("p_ItemVersionId_B", system.db.INTEGER, txnData['ItemVersionId_B'])
            call.registerInParam("p_WorkOrderId_A", system.db.INTEGER, txnData['WorkOrderId_A'])
            call.registerInParam("p_WorkOrderId_B", system.db.INTEGER, txnData['WorkOrderId_B'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        txn.handleUncaughtExceptions(ex, baseTagPath)


def spineModuleMovement_NORESP(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        spineSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/ProcessStep', '/SerialNumber'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            spineSerialNumber = util.parseBarcode_Last(txnData["SerialNumber"])
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_Conveyance_SpineModuleMovement_NORESP]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_SpineSerialNumber", system.db.NVARCHAR, spineSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN_NORESP(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        system.tag.writeBlocking(baseTagPath+"/Status/_MES_Log", str(ex))


def palletONLYMovement_NORESP(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/ProcessStep', '/SerialNumber'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            palletSerialNumber = util.parseBarcode_Last(txnData["SerialNumber"])
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_Conveyance_PalletONLYMovement_NORESP]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN_NORESP(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        system.tag.writeBlocking(baseTagPath+"/Status/_MES_Log", str(ex))


def palletMovement_NORESP(baseTagPath):
    try:
        # Default Function Variables
        txn_start = system.date.toMillis(system.date.now())
        readError = False
        txnData = {}
        # Script Specific Variables
        palletSerialNumber = ''

        ###################################
        # DATA COLLECTION
        try:
            txnInputs = [
                '/Inputs/ItemVersionId', '/Inputs/WorkOrderId', '/Inputs/AssetId', '/Inputs/UserName', '/Inputs/ProcessStep', '/SerialNumber'
            ]
            txnData = txn.getTXNData(txnInputs, baseTagPath)

            # Looping over read values for null
            for data in txnData:
                if txnData[data] is None:
                    raise Exception("Found null value at "+data)
            # Individual PLCData values to handle
            palletSerialNumber = util.parseBarcode_Last(txnData["SerialNumber"])
        except Exception as e:
            readError = True

        # SPROC PREP
        call = system.db.createSProcCall("[txn].[sp_Conveyance_PalletMovement_NORESP]", "REVMES")
        if not readError:
            call.registerInParam("p_ItemVersionId", system.db.INTEGER, txnData['ItemVersionId'])
            call.registerInParam("p_WorkOrderId", system.db.INTEGER, txnData['WorkOrderId'])
            call.registerInParam("p_PalletSerialNumber", system.db.NVARCHAR, palletSerialNumber)
            call.registerInParam("p_AssetId", system.db.INTEGER, txnData['AssetId'])
            call.registerInParam("p_Username", system.db.NVARCHAR, txnData['UserName'])
            call.registerInParam("p_ProcessStep", system.db.INTEGER, txnData['ProcessStep'])
        # Performing standard TXN Actions (Sproc, Response, Log)
        txn.performTXN_NORESP(baseTagPath, txn_start, readError, txnData, call)
    # For any other exception not handled
    except Exception as ex:
        system.tag.writeBlocking(baseTagPath+"/Status/_MES_Log", str(ex))
