.ia_powerChartComponent {
    font-size: 12px;
    color: var(--neutral-100);
    border: 1px solid var(--border);
}

.ia_powerChartComponent__title,
.ia_powerChartComponent__tagBrowserContainer__title {
    font-weight: bold;
}

.ia_powerChartComponent__eventMarker__box__timeLabel,
.ia_powerChartComponent__labelText {
    font-size: 11px;
    text-anchor: start;
    fill: var(--neutral-50);
    pointer-events: none;
}

.ia_powerChartComponent__labelText--centered {
    font-size: 11px;
    text-anchor: middle;
    fill: var(--neutral-50);
    pointer-events: none;
}

.ia_powerChartComponent__tagBrowserContainer {
    border-right: 1px solid var(--border);
    background-color: var(--neutral-20);
}

.ia_powerChartComponent__tagBrowserContainer__tree {
    background-color: var(--neutral-10);
    border: 1px solid var(--border);
}

.ia_powerChartComponent__xTrace__line {
    stroke: var(--neutral-60);
    stroke-width: 1;
    stroke-opacity: 0.5;
    stroke-dasharray: 0;
    cursor: crosshair;
    pointer-events: none;
}

.ia_powerChartComponent__xTrace__box {
    width: 100px;
    fill: var(--neutral-10);
    opacity: 0.9;
    stroke: var(--neutral-60);
    stroke-width: 1;
    stroke-opacity: 0.5;
    stroke-dasharray: 0;
    pointer-events: none;
}


.ia_powerChartComponent__xTrace__box__timeMarker {
    text-anchor: start;
    fill: var(--neutral-40);
}

.ia_powerChartComponent__xTrace__dot {
    fill: var(--neutral-60);
}

.ia_powerChartComponent__baseline__line {
    stroke: var(--neutral-60);
    stroke-width: 1;
    stroke-opacity: 0.5;
    stroke-dasharray: 0;
    pointer-events: none;
}

.ia_powerChartComponent__baseline__label {
    fill: var(--neutral-50);
    font-size: 10px;
    font-weight: 100;
    pointerEvents: none;
}

.ia_powerChartComponent__band__body {
    fill: var(--neutral-60);
    fill-opacity: 0.5;
    stroke-width: 0;
    stroke-dasharray: 0;
    pointer-events: none;
}

.ia_powerChartComponent__band__label {
    fill: var(--neutral-50);
    font-size: 10px;
    font-weight: 100;
    pointerEvents: none;
}

.ia_powerChartComponent__timeAxis__values,
.ia_powerChartComponent__yAxis__label,
.ia_powerChartComponent__yAxis__values {
    stroke: none;
    fill: var(--neutral-50);
    font-size: 10px;
    font-weight: 100;
}

.ia_powerChartComponent__timeAxis__ticks,
.ia_powerChartComponent__yAxis__ticks {
    fill: none;
    stroke: var(--neutral-50);
}

.ia_powerChartComponent__timeAxis__axis,
.ia_powerChartComponent__yAxis__axis {
    fill: none;
    stroke: var(--neutral-50);
}

.ia_powerChartComponent__dateTimeSelection {
    color: var(--callToAction);
    font-weight: bold;
}

.ia_powerChartComponent__noDataDisplay {
    background-color: var(--neutral-20);
    border-top: 1px solid var(--border);
}

.ia_powerChartComponent__chart__popupMenu {
    font-size: 12px;
    color: var(--neutral-100);
    background-color: var(--neutral-20);
}

.ia_powerChartComponent__chart__popupMenu button {
    font-size: 14px;
}

.ia_powerChartComponent__chart__rangeSelector > svg {
    border: 1px solid var(--border);
}

.ia_powerChartComponent__chart__rangeSelector__popupMenu {
    font-size: 12px;
    color: var(--neutral-100);
    background-color: var(--neutral-10);
}

.ia_powerChartComponent__chart__rangeSelector__popupMenu__menuItem {}

.ia_powerChartComponent__chart__rangeSelector__popupMenu__menuItem--hover {
    color: var(--neutral-10);
    font-weight: bold;
    background-color: var(--callToAction);
}

.ia_powerChartComponent__dateTimeSelection__popupMenu__header {
    font-size: 14px;
    background-color: var(--neutral-60);
    border-bottom: 1px solid var(--border);
}

.ia_powerChartComponent__dateTimeSelection__popupMenu__header__icon {
    color: var(--neutral-70);
}

.ia_powerChartComponent__icon {
    color: var(--neutral-70);
    border: 1px solid var(--border);
}

.ia_powerChartComponent__icon--active {
    background-color: var(--neutral-40);
}

.ia_powerChartComponent__chartOptionBar--mobile {
    border-top: 1px solid var(--border);
}

.ia_powerChartComponent__chartOptionBar__popupMenu {
    font-size: 12px;
    color: var(--neutral-100);
    background-color: var(--neutral-10);
}

.ia_powerChartComponent__chartOptionBar__popupMenu__menuItem {}

.ia_powerChartComponent__chartOptionBar__popupMenu__menuItem--hover {
    color: var(--neutral-10);
    font-weight: bold;
    background-color: var(--callToAction);
}

.ia_powerChartComponent__settings {
    background-color: var(--neutral-20);
    border-left: 1px solid var(--border);
}

.ia_powerChartComponent__settings__header {
    background-color: var(--neutral-10);
    font-weight: bold;
}

.ia_powerChartComponent__settings__iconHeader {
    background-color: var(--neutral-10);
    border: 1px solid var(--border);
    font-weight: bold;
}

.ia_powerChartComponent__settings__header__link {
    font-weight: normal;
    color: var(--callToAction);
}

.ia_powerChartComponent__settings__tab {
    border-color: var(--border);
    background-color: var(--neutral-20);
}

.ia_powerChartComponent__settings__tab--active {
    background-color: var(--neutral-10);
}

.ia_powerChartComponent__settings__tab__content,
.ia_powerChartComponent__settings__tab__content .ia_table__headContainer {
    background-color: var(--neutral-10);
}

.ia_powerChartComponent__settings__tab__addLink {
    font-weight: bold;
    text-decoration: underline;
    color: var(--callToAction);
}

.ia_powerChartComponent__settings__tab__content__table__headerCell {}

.ia_powerChartComponent__penDataDisplay__table__headerCell {
    background-color: var(--neutral-30);
}

.ia_powerChartComponent__penDataDisplay__brushRangeGroupLabel {
    background-color: var(--div-9);
}

.ia_powerChartComponent__penDataDisplay__table__headerCell,
.ia_powerChartComponent__penDataDisplay__table__bodyCell,
.ia_powerChartComponent__penDataDisplay--mobile__pagination,
.ia_powerChartComponent__penDataDisplay--mobile__penTable,
.ia_powerChartComponent__penDataDisplay__brushRangeGroupLabel {
    border-top: 1px solid var(--border);
}

.ia_powerChartComponent__penDataDisplay__table__separatorCell {
    border-right: 1px solid var(--border);
}

.ia_powerChartComponent__settings__tab__content__table__bodyRowOdd,
.ia_powerChartComponent__penDataDisplay__table__bodyRowOdd {
    background-color: var(--neutral-20);
}

.ia_powerChartComponent__settings__tab__content__table__bodyRowEven,
.ia_powerChartComponent__penDataDisplay__table__bodyRowEven {
    background-color: var(--neutral-10);
}

.ia_powerChartComponent__penDataDisplay__table__bodyRowOdd.overlay-shown,
.ia_powerChartComponent__penDataDisplay__table__bodyRowEven.overlay-shown,
.ia_powerChartComponent__penDataDisplay__table__rowOverlay {
    background-color: var(--red-20) !important;
}

.ia_powerChartComponent__penDataDisplay__table__rowOverlay__icon {
    color: var(--red-60) !important;
}

.ia_powerChartComponent__settings__tab__content__table__bodyCell,
.ia_powerChartComponent__penDataDisplay__table__bodyCell {}

.ia_powerChartComponent__settings__tab__content__table__bodyCell__icon,
.ia_powerChartComponent__penDataDisplay__table__bodyCell__icon,
.ia_powerChartComponent__penDataDisplay__table__icon,
.ia_powerChartComponent__penDataDisplay--mobile__icon,
.ia_powerChartComponent__tagBrowserContainer__icon {
    color: var(--neutral-70);
}

.ia_powerChartComponent__settings__tab__content__table__emptyMessage {
    color: var(--neutral-70);
}

.ia_powerChartComponent__settings__tab__content__popupMenu {
    font-size: 12px;
    color: var(--neutral-100);
}

.ia_powerChartComponent__settings__tab__content__buttonContainer {
    border: 1px solid var(--border);
    background-color: var(--neutral-10);
}

.ia_powerChartComponent__settings__tab__content__chartExample {
    border: 1px solid var(--border);
}

.ia_powerChartComponent__penLegend {
    border: 1px solid var(--border);
}

.ia_powerChartComponent__penLegend__navArrow {
    background-color: var(--neutral-30);
}

.ia_powerChartComponent__resize-handle.dragging::after {
    border: 2px solid var(--border);
}
