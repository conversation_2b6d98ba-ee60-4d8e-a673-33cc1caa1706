from java.lang import Exception as JavaException
import json
import traceback
import system.net
import system.util

_logger = system.util.getLogger('proterra.pack.api')

_api_base = 'https://vf.proterra.com/api/public'
# TODO: Do not hard code this
_api_token = 'APIKEY P0SOJfAaACulTzM4TpX3AeiNOyOEmf55XT5wLf3htCI='
_api_headers = {'Authorization': _api_token}


def get_module_pack(module_serial):
    """
    Get info about the pack for a module

    :param module_serial: The module serial number
    :returns: The pack information dict
    """
    pack_info = {}
    try:
        client = system.net.httpClient()
        url = '{}/queries/GVB_B3MES_GetModulePack?ModuleSerialNumber={}'
        response = client.get(url.format(_api_base, module_serial), headers=_api_headers)
        if response.good:
            json_pack = response.json[0][0]
            return {
                'startTime': json_pack.get('StartTime', None),
                'endTime': json_pack.get('EndTime', None),
                'workOrder': json_pack.get('WorkOrderNumber', None),
                'serial': json_pack.get('Serial', None),
                'barcode': json_pack.get('Barcode', None)
            }
    except (Exception, JavaException):
        print(traceback.format_exc())
        _logger.error(traceback.format_exc())
    return pack_info


def get_pack_info(serial):
    """
    Get info about a pack

    :param serial: The pack serial number
    :returns: The pack information dict
    """
    pack_info = {}
    try:
        client = system.net.httpClient()
        url = '{}/queries/GVB_B3MES_SerialNumberOrderInfo?serialnumber={}'
        response = client.get(url.format(_api_base, serial), headers=_api_headers)
        if response.good:
            pack_info = _parse_pack_info(response.json)
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return pack_info


def get_reprint_info(serial):
    """
    Get reprint info about a pack

    :param serial: The pack serial number
    :returns: The pack information dict
    """
    pack_info = {}
    try:
        client = system.net.httpClient()
        url = '{}/queries/GVB_B3MES_SerialNumberReprintInfo?serialnumber={}'
        response = client.get(url.format(_api_base, serial), headers=_api_headers)
        if response.good:
            json_result = json.loads(response.json[0][0]['jsonResult'])
            pack_info = json_result[0]
    except (Exception, JavaException):
        _logger.error(traceback.format_exc())
    return pack_info


def _parse_pack_info(response):
    """
    Parse information returned from VF about a pack

    Example response data:
    [
        [
            {
                "$type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib],[System.Object, System.Private.CoreLib]], System.Private.CoreLib",
                "jsonResult": "[{\"Part\":{\"PartCode\":\"066300\",\"PartDesc\":\"ASSEMBLY, S1-15-6S BATTERY, INVERTED, DAIMLER\",\"PartRevision\":\"A01\",\"PartID\":579138},\"workorder\":{\"MfgWorksOrderId\":109672,\"WorksOrderNumber\":\"GVB-1399538_PL2\",\"OrderQty\":1.500000000000000e+001,\"QtyMade\":1.500000000000000e+001,\"Status\":40,\"StatusName\":\"Completed\",\"Facility\":\"GVB\",\"EditUser\":\"JSBryant\",\"EditDate\":\"2024-12-03T17:48:20.007\",\"RouteVersionId\":20719,\"RouteID\":2951,\"SegmentID\":192,\"SegmentName\":\"GVB-BATTERYPACKLINE\"},\"serialnumber\":{\"MfgWorksOrderId\":109672,\"MfgLotID\":9737213,\"MfgLotSequence\":13,\"MfgSerialNumberId\":8962726,\"SerialNumber\":\"2000026250\"}}]"
            }
        ]
    ]

    Example return data:
    {'displayName': u'GVB-BATTERYPACKLINE', 'itemDesc': u'ASSEMBLY, S1-15-6S BATTERY, INVERTED, DAIMLER', 'serialNumber': u'2000026250', 'rev': u'A01', 'itemSerialNoStateName': u'Completed', 'itemNo': u'066300', 'serialNumberId': 8962726}

    :param response: The response from the http client
    :returns: The pack information dict
    """
    json_result = json.loads(response[0][0]['jsonResult'])
    json_pack = json_result[0]
    json_part = json_pack.get('Part', {})
    json_wo = json_pack.get('workorder', {})
    json_serial = json_pack.get('serialnumber', {})
    return {
        'serialNumber': json_serial.get('SerialNumber', ''),
        'serialNumberId': json_serial.get('MfgSerialNumberId', ''),
        'itemNo': json_part.get('PartCode', ''),
        'itemDesc': json_part.get('PartDesc', ''),
        'rev': json_part.get('PartRevision', ''),
        'itemSerialNoStateName': json_wo.get('StatusName', ''),
        'displayName': json_wo.get('SegmentName', '')
    }
