.ia_fileUploadComponent {
    border: 1px dashed var(--border);
    border-radius: var(--borderRadius);
    font-size: 0.875rem;
    color: var(--neutral-70);
}

.ia_fileUploadComponent--uploading,
.ia_fileUploadComponent--error,
.ia_fileUploadComponent--dragTarget {
    border: var(--containerBorder);
    background-color: var(--callToActionHighlight);
}

.ia_fileUploadComponent__displayMessage {
    color: var(--neutral-70);
}

.ia_fileUploadComponent--error__displayMessage {
    color: var(--error);
}

.ia_fileUploadComponent__supportedFileTypes {
    font-size: 0.75rem;
    color: var(--neutral-70);
}

.ia_fileUploadComponent__supportedFileTypes--error {
    color: var(--error);
}

.ia_fileUploadComponent__supportedFileTypes__primaryMessage {
    font-weight: 700;
}

.ia_fileUploadComponent__supportedFileTypes__icon {
    fill: var(--icon);
}

.ia_fileUploadComponent__filesList {
    border-top: var(--containerBorder);
}

.ia_fileUploadComponent__filesList__file {
    color: var(--neutral-70);
}

.ia_fileUploadComponent__filesList__fileName {
    color: var(--info);
}

.ia_fileUploadComponent__filesList__okayIcon {
    fill: var(--success);
}

.ia_fileUploadComponent__filesList__loadingIcon {
    fill: var(--neutral-70);
}

.ia_fileUploadComponent__filesList--error__loadingIcon {
    fill: var(--error);
}

.ia_fileUploadComponent__fileSummary__completeIcon {
    fill: var(--success);
}

.ia_fileUploadComponent__fileSummary__clearIcon {
    fill: var(--icon);
    cursor: pointer;
}

.ia_fileUploadComponent__fileSummary__clearIcon__message {
    font-size: 0.75rem;
}

.ia_fileUploadComponent__fileSummary__message--success {
    font-size: 0.875rem;
}

.ia_fileUploadComponent__pagination {
    background-color: var(--container);
    border-top: 1px solid var(--border);
}

.ia_fileUploadComponent__pagination__fileCount {
    font-size: 0.75rem;
    color: var(--neutral-70);
}

.ia_fileUploadComponent__pagination__nextPrevIcon {
    fill: var(--neutral-90);
}

.ia_fileUploadComponent__pagination__nextPrevIcon--disabled {
    fill: var(--neutral-50);
}

.ia_fileUploadComponent__pagination_callToAction:hover:not(.disabled) .ia_fileUploadComponent__pagination__nextPrevIcon {
    fill: var(--callToAction);
}

.ia_fileUploadComponent--tablet {
    border: 1px dashed var(--border);
    border-radius: var(--borderRadius);
    font-size: 0.75rem;
    color: var(--neutral-70);
}

.ia_fileUploadComponent--tablet__fileSummary__message--success {
    font-size: 0.75rem;
}

/*  */
.ia_fileUploadComponent--tablet__progress__uploadProgressAnimation {
    border: var(--containerBorder);
    background: var(--neutral-10);
}

/*  */
.ia_fileUploadComponent--tablet__progress__uploadProgressAnimation__innerPath {
    fill: var(--callToAction);
}

.ia_fileUploadComponent--mobile {
    border: 1px dashed var(--neutral-40);
    border-radius: var(--borderRadius);
    font-size: 0.75rem;
    color: var(--neutral-70);
}

.ia_fileUploadComponent--mobile--invalid {
    border-color: var(--callToAction);
}

.ia_fileUploadComponent--mobile--popupDisplayed {
    border-color: var(--callToAction);
}

.ia_fileUploadComponent--mobile--uploading,
.ia_fileUploadComponent--mobile--error,
.ia_fileUploadComponent--mobile--complete {
    border: 1px solid var(--callToAction);
}

.ia_fileUploadComponent--mobile--complete__icon {
    fill: var(--success);
}

.ia_fileUploadComponent--mobile--error__icon {
    fill: var(--error);
}

.ia_fileUploadComponent--mobile__popup {
    color: var(--neutral-70);
}

.ia_fileUploadComponent--mobile__filesList {
    border-top: var(--containerBorder);
}

.ia_fileUploadComponent--mobile__uploadIcon {
    fill: var(--callToAction);
}

.ia_fileUploadComponent__supportedFileTypes__icon {
    fill: var(--icon);
}

.ia_fileUploadComponent--mobile__progress__activeFileText {
    fill: var(--neutral-90);
}

.ia_fileUploadComponent--mobile__progress__uploadProgressAnimation {
    border: var(--containerBorder);
    background: var(--neutral-10);
}

.ia_fileUploadComponent--mobile__progress__uploadProgressAnimation__innerPath {
    fill: var(--callToAction);
}
