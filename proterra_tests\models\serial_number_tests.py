import platform
from proterra.models import serial_number
from proterra.models.serial_number import SerialNumberException
from proterra.testing.mock_classes import MockTimestamp
from datetime import datetime
from unittest import TestCase, TestLoader, TestSuite, TextTestRunner
from mock import patch, MagicMock


@patch('system.db.runPrepQuery')
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class GetSerialNumberTests(TestCase):
    def setUp(self):
        self.test_dataset = [{
            'ItemSerialNoId': 420420,
            'ItemVersionId': 1,
            'SerialNoNumber': 'M420420',
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'SerialNumberTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'SerialNumberTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000)
        }]

    def test_get_serial_number_by_id(self, runPrepQuery):
        runPrepQuery.return_value = self.test_dataset
        sn = serial_number.get_serial_number_by_id(420420)
        self.assertEqual('M420420', sn.SerialNoNumber)

    def test_get_serial_number_by_number(self, runPrepQuery):
        runPrepQuery.return_value = self.test_dataset
        sn = serial_number.get_serial_number_by_number('M420420')
        self.assertEqual(420420, sn.ItemSerialNoId)

    def test_get_serial_number_by_id_not_found(self, runPrepQuery):
        runPrepQuery.return_value = []
        sn = serial_number.get_serial_number_by_id(420420)
        self.assertEqual('', sn.SerialNoNumber)

    def test_get_serial_number_by_number_not_found(self, runPrepQuery):
        runPrepQuery.return_value = []
        sn = serial_number.get_serial_number_by_number('M420420')
        self.assertEqual(-1, sn.ItemSerialNoId)

    def test_get_serial_number_by_id_exception(self, runPrepQuery):
        runPrepQuery.side_effect = Exception('SerialNumberTests')
        with self.assertRaises(SerialNumberException):
            serial_number.get_serial_number_by_id(420420)

    def test_get_serial_number_by_number_exception(self, runPrepQuery):
        runPrepQuery.side_effect = Exception('SerialNumberTests')
        with self.assertRaises(SerialNumberException):
            serial_number.get_serial_number_by_number('M420420')


@patch('system.dataset.toPyDataSet')
@patch('system.db.createSProcCall', MagicMock())
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class InsertSerialNumberTests(TestCase):
    def setUp(self):
        self.test_dataset = [{
            'AssetId': 1,
            'CreatedBy': 'SerialNumberTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'SerialNumberTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'Result': 'Success',
            'ResultId': 420420,
            'ResultMessage': ''
        }]
        self.serial_number = [{
            'ItemSerialNoId': 420420,
            'ItemVersionId': 1,
            'SerialNoNumber': 'M420420',
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'SerialNumberTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'SerialNumberTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000)
        }]

    @patch('system.db.runPrepQuery')
    def test_insert_serial_number(self, runPrepQuery, toPyDataSet):
        toPyDataSet.return_value = self.test_dataset
        runPrepQuery.return_value = self.serial_number
        sn = serial_number.insert_serial_number(1, 'M420420', 1, 'SerialNumberTests')
        self.assertEqual('M420420', sn.SerialNoNumber)
        self.assertEqual(420420, sn.ItemSerialNoId)

    def test_insert_serial_number_not_found(self, toPyDataSet):
        toPyDataSet.return_value = []
        with self.assertRaises(SerialNumberException):
            sn = serial_number.insert_serial_number(1, 'M420420', 1, 'SerialNumberTests')

    def test_insert_serial_number_failure(self, toPyDataSet):
        self.test_dataset[0]['Result'] = 'Failure'
        toPyDataSet.return_value = self.test_dataset
        with self.assertRaises(SerialNumberException):
            sn = serial_number.insert_serial_number(1, 'M420420', 1, 'SerialNumberTests')

    def test_insert_serial_number_exception(self, toPyDataSet):
        toPyDataSet.side_effect = Exception('SerialNumberTests')
        with self.assertRaises(SerialNumberException):
            serial_number.insert_serial_number(1, 'M420420', 1, 'SerialNumberTests')


@patch('system.dataset.toPyDataSet')
@patch('system.db.createSProcCall', MagicMock())
@patch('system.db.execSProcCall', MagicMock())
@patch('system.db.runPrepUpdate', MagicMock())
@patch('system.db.beginTransaction', MagicMock())
@patch('system.db.commitTransaction', MagicMock())
@patch('system.db.closeTransaction', MagicMock())
class GenerateSerialNumberTests(TestCase):
    def setUp(self):
        self.test_dataset = [{
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'SerialNumberTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'SerialNumberTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'Result': 'Success',
            'ResultId': 420420,
            'ResultMessage': ''
        }]
        self.serial_number = [{
            'ItemSerialNoId': 420420,
            'ItemVersionId': 1,
            'SerialNoNumber': 'M420420',
            'ItemSerialNoStateId': 1,
            'AssetId': 1,
            'CreatedBy': 'SerialNumberTests',
            'CreatedAt': MockTimestamp(datetime.now().microsecond / 1000),
            'EditedBy': 'SerialNumberTests',
            'EditedAt': MockTimestamp(datetime.now().microsecond / 1000)
        }]

    @patch('system.db.runPrepQuery')
    def test_generate_serial_number(self, runPrepQuery, toPyDataSet):
        toPyDataSet.return_value = self.test_dataset
        runPrepQuery.return_value = self.serial_number
        sn = serial_number.generate_serial_number(1, 1, 'M', 1, -1, 'SerialNumberTests')
        self.assertEqual('M420420', sn.SerialNoNumber)
        self.assertEqual(420420, sn.ItemSerialNoId)

    def test_generate_serial_number_not_found(self, toPyDataSet):
        toPyDataSet.return_value = MagicMock(return_value=[])
        with self.assertRaises(SerialNumberException):
            sn = serial_number.generate_serial_number(1, 1, 'M', 1, -1, 'SerialNumberTests')

    def test_generate_serial_number_failure(self, toPyDataSet):
        self.test_dataset[0]['Result'] = 'Failure'
        toPyDataSet.return_value = self.test_dataset
        with self.assertRaises(SerialNumberException):
            sn = serial_number.generate_serial_number(1, 1, 'M', 1, -1, 'SerialNumberTests')

    def test_generate_serial_number_exception(self, toPyDataSet):
        toPyDataSet.side_effect = Exception('SerialNumberTests')
        with self.assertRaises(SerialNumberException):
            serial_number.generate_serial_number(1, 1, 'M', 1, -1, 'SerialNumberTests')


if platform.python_implementation() == 'Jython':
    def run():
        test_cases = (
            GetSerialNumberTests,
            InsertSerialNumberTests,
            GenerateSerialNumberTests)
        suites = []
        for test_case in test_cases:
            suite = TestLoader().loadTestsFromTestCase(test_case)
            suites.append(suite)
        TextTestRunner(verbosity=2).run(TestSuite(suites))

    if __name__ == '__main__':
        run()
