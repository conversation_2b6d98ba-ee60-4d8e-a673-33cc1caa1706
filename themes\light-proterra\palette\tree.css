.ia_treeComponent__node {
    font-size: 0.75rem;
    line-height: 1rem;
}

.ia_treeComponent__node:hover:not(.ia_treeComponent__node--selected) {
    background-color: var(--callToActionHighlight);
}

.ia_treeComponent__node--selected {
    background-color: var(--callToAction);
    color: var(--neutral-10);
    font-weight: 700;
}

.ia_treeComponent__node__icon {
    fill: var(--icon);
}

.ia_treeComponent__expandIcon,
.ia_treeComponent__terminalExpandIcon,
.ia_treeComponent__node__icon--expanded {
    fill: var(--icon);
}

.ia_treeComponent__node__icon--selected,
.ia_treeComponent__expandIcon--selected {
    fill: var(--icon--selected);
}

.ia_treeComponent__alignmentGuide {
    color: var(--border);
}

.ia_treeComponent__alignmentGuide--selected {
    color: var(--neutral-10);
}


/********** MOBILE **********/


.ia_treeComponent__node.mobile {
    font-size: 1.5rem;
}
